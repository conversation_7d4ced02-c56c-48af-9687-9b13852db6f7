openapi: 3.0.3
info:
  title: Postman API
  description: |
    The Postman API enables you to programmatically access data stored in your Postman account.

    > Certain endpoints may be unavailable depending on your region and/or Postman plan.

    For a comprehensive set of examples of requests and responses, see the [**Postman API** collection](https://www.postman.com/postman/workspace/postman-public-workspace/documentation/********-c8142d51-e97c-46b6-bd77-52bb66712c9a).

    ## Getting started

    You can get started with the Postman API by creating a copy of this definition in your workspace.

    ### EU users

    For users in the EU with [**Enterprise** plans](https://www.postman.com/pricing/), the Postman API uses the `http://api.eu.postman.com` subdomain. This is available in the definition's list of servers. You can change this by selecting the `http://api.eu.postman.com` subdomain in the **Server** dropdown list below.

    ## About the Postman API

    - You must use a valid API Key to send requests to the API endpoints.
    - The API has [rate and usage limits](https://learning.postman.com/docs/developer/postman-api/postman-api-rate-limits/).
    - The API only responds to HTTPS-secured communications. Any requests sent via HTTP return an HTTP `301` redirect to the corresponding HTTPS resources.
    - The API returns requests responses in JSON format. When an API request returns an error, it is sent in the JSON response as an error key.
    - The request method (verb) determines the nature of action you intend to perform. A request made using the `GET` method implies that you want to fetch something from Postman. The `POST` method implies you want to save something new to Postman.
    - For all requests, API calls respond with their corresponding [HTTP status codes](https://en.wikipedia.org/wiki/List_of_HTTP_status_codes). In the Postman client, the status code also provides help text that details the possible meaning of the response code.
    - When calling the API Builder endpoints, you must send an `Accept` header with the `application/vnd.api.v10+json` value.

    ### IDs and UIDs

    All items in Postman, such as collections, workspaces, and APIs, have IDs and UIDs:

    - An ID is the unique ID assigned to a Postman item. For example, `ec29121c-5203-409f-9e84-e83ffc10f226`.
    - The UID is the **full** ID of a Postman item. This value is the item's unique ID concatenated with the user ID. For example, in the `12345678-ec29121c-5203-409f-9e84-e83ffc10f226` UID:
        - `12345678` is the user's ID.
        - `ec29121c-5203-409f-9e84-e83ffc10f226` is the item's ID.

    ### Enum values

    Any documented enum values should be considered partial lists and may change over time.

    ### 403 response for unavailable features

    Depending on your region and/or Postman [plan](https://www.postman.com/pricing/), some endpoints will return an HTTP `403 Forbidden` response with the "This feature isn't available in your region." detail.

    ### 503 response

    An HTTP `503 Service Unavailable` response from our servers indicates there is an unexpected spike in API access traffic. The server is usually operational within the next five minutes.

    If the outage persists or you receive any other form of an HTTP `5XX` error, [contact support](https://support.postman.com/hc/en-us/requests/new/).

    ## Authentication

    Postman uses API keys for authentication. The API key tells the API server that the request came from you. Everything that you have access to in Postman is accessible with your API key. You can [generate](https://learning.postman.com/docs/developer/postman-api/authentication/#generate-a-postman-api-key) a Postman API key in the [**API keys**](https://postman.postman.co/settings/me/api-keys) section of your Postman account settings.

    You must include an API key in each request to the Postman API with the `X-API-Key` request header. In Postman, you can store your API key as a [vault secret](https://learning.postman.com/docs/sending-requests/postman-vault/postman-vault-secrets/) or an [environment variable](https://www.getpostman.com/docs/environments). The Postman API [collection](https://www.getpostman.com/docs/collections) will use it to make API calls.

    ### SCIM authentication

    While all other endpoints in this collection require a Postman API key, the SCIM endpoints require a [SCIM API key](https://learning.postman.com/docs/administration/scim-provisioning/scim-provisioning-overview/#generating-scim-api-key).

    ### Authentication error response

    If an API key is missing, malformed, or invalid, you will receive an HTTP `401 Unauthorized` response code.

    ## Rate and usage limits

    API access [rate limits](https://learning.postman.com/docs/developer/postman-api/postman-api-rate-limits/) apply at a per-user basis in unit time. The limit is **300 requests per minute**. Postman Monitors, the GET `/collections`, and the GET `/workspaces` endpoint have a rate limit of **10 calls in 10 seconds**. Depending on your [plan](https://www.postman.com/pricing/), you may also have [usage limits](https://learning.postman.com/docs/billing/resource-usage/). Every API response includes headers to help you identify the status of your use limits. For more information, see [Track Postman API call limits](https://learning.postman.com/docs/developer/postman-api/postman-api-rate-limits/).

    When you reach your rate or usage limits, the API returns the following HTTP `429 Too Many Requests` status code with one of the following error responses:

    - `rateLimited` — Rate limits reached. The response returns the time after which you can resume calls to the Postman API. The response header also includes the `X-RateLimit-RetryAfter` and `Retry-After` responses when you go over your limit, which returns the seconds remaining until you can make another request.
    - `serviceLimitExhausted` — Postman API service limits reached. You will need to contact your Postman Team Admin for assistance.

    ## Support

    For help regarding accessing the Postman API, you can:

    - Visit [Postman Support](https://support.postman.com/hc/en-us) or our [Community and Support](https://www.postman.com/community/) sites.
    - Reach out to the [Postman community](https://community.postman.com/).
    - Submit a help request to [Postman support](https://support.postman.com/hc/en-us/requests/new/).

    ## Policies

    - [Postman Terms of Service](http://www.postman.com/legal/terms/)
    - [Postman Privacy Policy](https://www.postman.com/legal/privacy-policy/)
  version: '1.0'
  termsOfService: https://www.postman.com/legal/terms/
  contact:
    name: Postman Support
    email: <EMAIL>
    url: https://www.postman.com/community/
servers:
  - url: https://api.getpostman.com
  - url: https://api.eu.postman.com
security:
  - PostmanApiKey: []
  - scimApiKey: []
tags:
  - name: API
    description: The **API** endpoints enable you to manage your APIs. When using these endpoints, you must send an `Accept` header with the `application/vnd.api.v10+json` value.
  - name: API Security
    description: The **API Security** endpoints enable you to manage the [security of your API](https://learning.postman.com/docs/api-governance/api-definition/api-definition-warnings/). API security includes ensuring you follow security and format warnings and schema validation.
  - name: API Tags
    description: The **API Tags** endpoints enable you to manage API tags with Postman's [tagging](https://learning.postman.com/docs/designing-and-developing-your-api/managing-apis/#tagging-apis) feature. Tagging is available on Postman [**Enterprise** plans](https://www.postman.com/pricing/).
  - name: API Versions
    description: The **API Versions** endpoints enable you to manage your [API's versions](https://learning.postman.com/docs/designing-and-developing-your-api/versioning-an-api/versioning-an-api-overview/#publishing-an-api-version). Only users with the [API Admin](https://learning.postman.com/docs/collaborating-in-postman/roles-and-permissions/#api-roles) or [Workspace Admin](https://learning.postman.com/docs/collaborating-in-postman/roles-and-permissions/#workspace-roles) role can publish and manage versions.
  - name: Audit Logs
    description: The [**Audit Logs**](https://learning.postman.com/docs/administration/audit-logs/) endpoints enable you to monitor and analyze your Professional or Enterprise Postman teams. These endpoints are only available on Postman [**Professional** and **Enterprise** plans](https://www.postman.com/pricing).
  - name: Billing
    description: The **Billing** endpoints enable you to manage your Postman billing account, such as your team's plan and [billing](https://learning.postman.com/docs/billing/billing/) information.
  - name: Collection Access Keys
    description: |
      The **Collection Access Keys** endpoints enable you to manage your [collection access keys](https://learning.postman.com/docs/developer/postman-api/authentication/#generate-a-collection-access-key). Collection access keys enable you to securely grant other users read-only access to a collection.

      Collection access keys are valid for 60 days. If unused, the access key expires after 60 days. If someone uses the access key, then its expiration date increases by another 60 days.
  - name: Collections
    description: The **Collections** endpoints enable you to manage your Postman [Collections](https://learning.postman.com/docs/sending-requests/intro-to-collections/).
  - name: Collection Items
    description: The **Collection Items** endpoints enable you to manage a collection's items, such as folders and requests.
  - name: Collection Requests
    description: The **Collection Requests** endpoints enable you to manage collection requests.
  - name: Collection Folders
    description: The **Collection Folders** endpoints enable you to manage collection folders.
  - name: Collection Responses
    description: The **Collection Responses** endpoints enable you to manage collection responses.
  - name: Collection Tags
    description: The **Collection Tags** endpoints enable you to manage collection tags with Postman's [tagging](https://learning.postman.com/docs/collections/using-collections/#tagging-a-collection) feature. Tagging is available on Postman [**Enterprise** plans](https://www.postman.com/pricing/).
  - name: Collection to API
    description: The **Collection to API** endpoints enable you to transform your Postman Collections into API definitions.
  - name: Comments
    description: The **Comments** endpoints enable you to manage comments on an API element. You can use [comments](https://learning.postman.com/docs/collaborating-in-postman/working-with-your-team/discussing-your-work/) to collaborate and discuss your work with your teammates in Postman.
  - name: Detected Secrets
    description: The **Detected Secrets** endpoints enable you to manage secrets detected by the Postman [Secret Scanner](https://learning.postman.com/docs/administration/secret-scanner/).
  - name: Environments
    description: The **Environments** endpoints enable you to manage your Postman [environments](https://learning.postman.com/docs/sending-requests/managing-environments/).
  - name: Forks
    description: The **Forks** endpoints enable you to manage element [forks](https://learning.postman.com/docs/collaborating-in-postman/using-version-control/forking-elements/). Forks are new instances of an element that you can change without making any changes to the parent element.
  - name: Global Variables
    description: The **Global Variables** endpoints enable you to manage global [variables](https://learning.postman.com/docs/sending-requests/variables/). Global variables are available throughout a workspace and have the broadest scope in Postman.
  - name: Group Provisioning
    description: The **Group Provisioning** endpoints enable you to provision and manage your Postman groups.
  - name: Groups
    description: The **Groups** endpoints enable you to manage user [groups](https://learning.postman.com/docs/collaborating-in-postman/user-groups/) in Postman. With groups, you can arrange users into groups that reflect your organizational structure.
  - name: Import
    description: The **Import** endpoints enable you to manage [importing and exporting](https://learning.postman.com/docs/getting-started/importing-and-exporting-data/) Postman data.
  - name: Mocks
    description: The **Mocks** endpoints enable you to manage your [mock servers](https://learning.postman.com/docs/designing-and-developing-your-api/mocking-data/setting-up-mock/).
  - name: Monitors
    description: The **Monitors** endpoints enable you to manage your [monitors](https://learning.postman.com/docs/monitoring-your-api/intro-monitors/).
  - name: Pull Requests
    description: These endpoints enable you to manage your [pull requests](https://learning.postman.com/docs/collaborating-in-postman/using-version-control/creating-pull-requests/) in Postman. Pull requests allow reviewers to look at your changes. Reviewers can make comments on your changes and will decide whether to approve them and merge them into the parent element.
  - name: Private API Network
    description: The **Private API Network** endpoints enable you to manage and work with your team's [Private API Network](https://learning.postman.com/docs/collaborating-in-postman/adding-private-network/). These endpoints are only available on Postman [**Enterprise** plans](https://www.postman.com/pricing/#enterprise-solutions).
  - name: Roles
    description: The **Roles** endpoints enable you to manage user roles. [Roles])(https://learning.postman.com/docs/collaborating-in-postman/roles-and-permissions/) define user permissions within a Postman workspace.
  - name: Schema
    description: The **Schema** endpoints enable you to manage your API definitions. These endpoints also support multi-file schema.
  - name: SCIM
    description: |
      Postman supports [SCIM](https://learning.postman.com/docs/administration/scim-provisioning/scim-provisioning-overview/) (System for Cross-domain Identity Management), which allows you to automate your team's provisioning. You can deploy Postman at scale across your organization and control access to it with your identity provider.

      SCIM provisioning is only available with a Postman [**Enterprise** plan](https://www.postman.com/pricing).

      **Note:**

      - While all other endpoints in this collection require a Postman API key, **these endpoints require a [SCIM API key](https://learning.postman.com/docs/administration/scim-provisioning/scim-provisioning-overview/#generating-scim-api-key)**.
      - You must be a [Team Admin](http://learning.postman.com/docs/collaborating-in-postman/roles-and-permissions/#team-roles) to enable SCIM.
      - To use SCIM, you must have only **one** SSO method configured. If you have more than one SSO method enabled, you cannot generate an SCIM API key.
      - You can only deactivate users with the SCIM API. You cannot permanently delete users with the API.
      - You must [configure SSO](https://learning.postman.com/docs/administration/sso/admin-sso/) and [enable SCIM](http://learning.postman.com/docs/administration/managing-your-team/configuring-scim/#enabling-scim-provisioning) for your Postman team to use these endpoints.

      ### Rate limits

      For your organization's team, Postman applies per-minute rate limits across all SCIM API endpoints. This helps ensure that you have the best experience using Postman's SCIM API. The rate limits apply as follows:
      - **Write (POST, PUT, PATCH)** — 180 requests per minute.
      - **Read (GET)** — 300 requests per minute.

      If you reach the rate limit, the API will return an HTTP `429 Too Many Requests` response.
  - name: Secret Scanner
    description: The **Secret Scanner** endpoints enable you to manage the secrets detected by Postman's [Secret Scanner](https://learning.postman.com/docs/administration/secret-scanner/). These endpoints are only available on [**Enterprise** plans](https://www.postman.com/pricing/#enterprise-solutions) with the **Advanced Security Administration** add-on feature.
  - name: Tags
    description: The **Tags** endpoints enable you to manage Postman's tagging feature. Tagging is available on Postman [**Enterprise** plans](https://www.postman.com/pricing/).
  - name: Transfer Collection Items
    description: The **Transfer Collection Items** endpoints enable you to move collection items between different collections or folders.
  - name: Users
    description: The **Users** endpoints enable you to get information about Postman users on your team.
  - name: User Provisioning
    description: |
      The **User Provisioning** endpoints enable you to provision and manage your Postman users.

      - You can only deactivate users with the SCIM API. You cannot permanently delete users with the API.
      - Users created with the SCIM API are automatically created in Postman:
        - If the account's email ID does not exist, the user is also added to their organization's Postman team.
        - If the account's email ID already exists, the system sends the user an email invite to join the Postman team. After they accept the invite, the are added to the team.
        - If the user's email domain matches your team's verified domains, the user is immediately added to the team.
      - By default, new users are given the developer role in Postman. Postman user roles cannot be updated via the SCIM API. You must manage user and group roles in Postman.
  - name: Workspaces
    description: The **Workspaces** endpoints enable you to manage your [workspaces](https://learning.postman.com/docs/collaborating-in-postman/using-workspaces/creating-workspaces/). The Workspaces endpoints let you manage your workspaces. Workspaces enable you to organize your Postman work and collaborate with teammates.
  - name: Workspace Tags
    description: The **Workspace Tags** endpoints enable you to manage workspace tags with Postman's [tagging](https://learning.postman.com/docs/collaborating-in-postman/using-workspaces/managing-workspaces/#tagging-a-workspace) feature. Tagging is available on Postman  [**Enterprise** plans](https://www.postman.com/pricing/).
  - name: Webhooks
    description: The **Webhooks** endpoints enable you to manage [custom webhooks](https://learning.postman.com/docs/running-collections/collection-webhooks/).
