schemas:
    invoicesAccountInfo:
      title: Account Information
      type: object
      description: Information about the account.
      properties:
        billingEmail:
          type: string
          format: email
          description: The email address to which invoices are sent.
          example: <EMAIL>
        id:
          type: integer
          description: The account's ID.
          example: ********
        state:
          type: string
          description: |
            The account's current state:
            - `FREE`
            - `PAID`
            - `EXPIRED_TRIAL`
            - `OVERDUE`
            - `SUSPENDED`
            - `BLOCKED`
            - `PAYMENT_DISPUTED`
          example: PAID
        teamId:
          type: integer
          description: The team's ID associated with the account.
          example: 1234
        salesChannel:
          type: string
          description: |
            The sales channel from which the account was created:
            - `SELF_SERVE` — The user purchased the account plan.
            - `SALES_SERVE` — The account was purchased through the Postman sales team process.
          enum:
            - SELF_SERVE
            - SALES_SERVE
          example: SELF_SERVE
        slots:
          type: object
          description: Information about the team's slots.
          properties:
            available:
              type: integer
              description: The number of the team's available slots.
              example: 8
            consumed:
              type: integer
              description: The number of currently-billed team members.
              example: 2
            total:
              type: integer
              description: The total number of slots available to the team.
              example: 10
            unbilled:
              type: integer
              description: The number of unbilled slots if [auto-flex billing](https://learning.postman.com/auto-flex-policy/) is available.
              example: 0
    invoicesNoTeam400Error:
      title: Bad Request
      type: object
      properties:
        type:
          type: string
          format: uri
          description: The error type.
          example: https://api.postman.com/problems/bad-request
        title:
          type: string
          description: A short summary of the error.
          example: Malformed request
        status:
          type: number
          format: http-status-code
          description: The error's HTTP status code.
          example: 400
        detail:
          type: string
          description: Details about the occurrence of the error.
          example: You must be part of a team
    invoiceData:
      title: Account Invoice
      type: object
      description: Information about the invoice.
      properties:
        id:
          type: string
          description: The invoice's ID.
          example: inv_7UDSYBJPGQU93N7M
        status:
          type: string
          description: The invoice's status.
          example: PAID
        issuedAt:
          type: string
          format: date
          description: The date on which the invoice was issued.
          example: '2023-10-12'
        totalAmount:
          type: object
          description: Information about the invoice's total billed amount.
          properties:
            value:
              type: integer
              description: The amount billed.
              example: 440
            currency:
              type: string
              description: The currency of the billed amount. Currently only supports the `USD` value.
              example: USD
        links:
          type: object
          description: A [JSON API spec](https://jsonapi.org/format/#document-links) object containing hypermedia links.
          properties:
            web:
              type: object
              description: An object containing web-based account references.
              properties:
                href:
                  type: string
                  format: url
                  description: A URL where you can download the invoice in PDF and view details.
                  example: https://pay.postman.com/invoices/pay?invoice_public_id=inv_7UDSYBJPGQU93N7M
    getAccountInvoices:
      type: object
      required:
        - data
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/invoiceData'
    invoiceMissingStatus400Error:
      title: Missing Status
      type: object
      properties:
        type:
          type: string
          format: uri
          description: The error type.
          example: https://api.postman.com/problems/bad-request
        title:
          type: string
          description: A short summary of the error.
          example: Malformed request
        status:
          type: number
          format: http-status-code
          description: The error's HTTP status code.
          example: 400
        detail:
          type: string
          description: Details about the occurrence of the error.
          example: Please provide a valid status to fetch invoices
    invoicesForbidden403Error:
      title: Forbidden
      type: object
      properties:
        type:
          type: string
          format: uri
          description: The error type.
          example: https://api.postman.com/problems/forbidden
        title:
          type: string
          description: A short summary of the error.
          example: Forbidden
        status:
          type: number
          format: http-status-code
          description: The error's HTTP status code.
          example: 403
        detail:
          type: string
          description: Details about the occurrence of the error.
          example: You are not authorized to perform this action
    workspaceId:
      type: string
      example: 1f0df51a-8658-4ee8-a2a1-d2567dfa09a9
    cursor:
      type: string
      example: RnJpIEZlYiAyNCAyMDIzIDEzOjI0OjA5IEdNVCswMDAwIChDb29yZGluYXRlZCBVbml2ZXJzYWwgVGltZSk=
    limit:
      type: integer
      default: 10
      example: 10
    featureUnavailable403Error:
      title: Feature Unavailable
      type: object
      properties:
        type:
          type: string
          format: uri-reference
          description: The [URI reference](https://www.rfc-editor.org/rfc/rfc3986) that identifies the type of problem.
          example: https://api.postman.com/problems/forbidden
        title:
          type: string
          description: A short summary of the problem.
          example: Forbidden
        detail:
          type: string
          description: Information about the error.
          example: This feature isn't available in your region.
        status:
          type: integer
          format: http-status-code
          description: The error's HTTP status code.
          example: 403
    api404ErrorNotFound:
      title: API Not Found
      type: object
      properties:
        type:
          type: string
          description: The type of error.
          example: instanceNotFoundError
        title:
          type: string
          description: A short summary of the problem.
          example: We could not find the API you are looking for
        detail:
          type: string
          description: Information about the error.
          example: We could not find the API you are looking for
    workspace400ErrorParamMissing:
      title: Missing Workspace ID
      type: object
      properties:
        type:
          type: string
          description: The type of error.
          example: paramMissingError
        title:
          type: string
          description: A short summary of the problem.
          example: Parameter is missing in the request.
        detail:
          type: string
          description: Information about the error.
          example: Parameter, workspaceId is missing in the request.
    api403ErrorForbidden:
      title: Forbidden
      type: object
      properties:
        type:
          type: string
          description: The type of error.
          example: ForbiddenError
        title:
          type: string
          description: A short summary of the problem.
          example: You are not permitted to perform the action.
        detail:
          type: string
          description: Information about the error.
          example: Please ensure that you have required permissions
    api403ErrorLimitReached:
      title: API Limit Reached
      type: object
      properties:
        type:
          type: string
          description: The type of error.
          example: limitReachedError
        title:
          type: string
          description: A short summary of the problem.
          example: You've reached the API limit
        message:
          type: string
          description: The error message.
          example: You can create up to 3 APIs on your current plan.
        details:
          type: object
          description: Information about the error.
          properties:
            model:
              type: string
              description: The `api` model.
              enum:
                - api
              example: api
            action:
              type: string
              description: The `create` action.
              enum:
                - create
              example: create
            message:
              type: string
              description: The error message.
              example: You can create up to 3 APIs on your current plan.
    workspace404Error:
      title: Workspace Not Found
      type: object
      properties:
        type:
          type: string
          description: The type of error.
          example: instanceNotFoundError
        title:
          type: string
          description: A short summary of the problem.
          example: We could not find the workspace you are looking for
        detail:
          type: string
          description: Information about the error.
          example: We could not find the workspace you are looking for.
    v10HeaderMissing:
      title: Missing v10 Accept Header
      type: object
      properties:
        name:
          type: string
          description: The error name.
          example: instanceNotFoundError
        message:
          type: string
          description: The error message.
          example: API not found. For v10 and later APIs, ensure that your request sends the "application/vnd.api.v10+json" Accept header.
    apiSchema403ErrorForbidden:
      title: Forbidden
      type: object
      properties:
        type:
          type: string
          description: The type of error.
          example: ForbiddenError
        title:
          type: string
          description: A short summary of the problem.
          example: You are not permitted to perform the action.
        detail:
          type: string
          description: Information about the error.
          example: You do not have access to perform this operation.
    apiVersionId:
      type: string
      example: 12ece9e1-2abf-4edc-8e34-de66e74114d2
    api400ErrorVersionIdMissing:
      title: Version ID Missing
      type: object
      properties:
        type:
          type: string
          description: The error type.
          example: invalidParamError
        title:
          type: string
          description: A short summary of the problem.
          example: Invalid request body
        detail:
          type: string
          description: Details about the error.
          example: Missing field 'versionId' in the request body
    apiCollection404ErrorNotFound:
      title: API Collection Not Found
      type: object
      properties:
        error:
          type: object
          properties:
            type:
              type: string
              description: The type of error.
              example: instanceNotFoundError
            title:
              type: string
              description: A short summary of the problem.
              example: Collection not found
            detail:
              type: string
              description: Information about the error.
              example: The API does not contain the collection you are looking for
    commentResponse:
      title: Comment Response Object
      type: object
      properties:
        data:
          type: array
          required:
            - id
            - threadId
            - createdBy
            - createdAt
            - updatedAt
            - body
          items:
            type: object
            description: Information about the comment.
            properties:
              id:
                type: integer
                description: The comment's ID.
                example: 46814
              threadId:
                type: integer
                description: The comment's thread ID.
                example: 12345
              createdBy:
                type: integer
                description: The user ID of the user who created the comment.
                example: ********
              createdAt:
                type: string
                format: date-time
                description: The date and time at which the comment was created.
                example: '2024-01-18T11:30:40.000Z'
              updatedAt:
                type: string
                format: date-time
                description: The date and time when the comment was last updated.
                example: '2024-01-18T11:30:40.000Z'
              body:
                type: string
                description: The contents of the comment.
                example: This is an example.
    comment401Error:
      title: Unauthorized
      type: object
      properties:
        type:
          type: string
          format: uri-reference
          description: The [URI reference](https://www.rfc-editor.org/rfc/rfc3986) that identifies the type of problem.
          example: https://api.postman.com/problems/unauthorized
        title:
          type: string
          description: A short summary of the problem.
          example: Unauthorized
        detail:
          type: string
          description: Information about the error.
          example: Unauthorized
        status:
          type: integer
          format: http-status-code
          description: The error's HTTP status code.
          example: 401
    comment403Error:
      title: Forbidden
      type: object
      properties:
        type:
          type: string
          format: uri-reference
          description: The [URI reference](https://www.rfc-editor.org/rfc/rfc3986) that identifies the type of problem.
          example: https://api.postman.com/problems/forbidden
        title:
          type: string
          description: A short summary of the problem.
          example: Forbidden
        detail:
          type: string
          description: Information about the error.
          example: Forbidden
        status:
          type: integer
          format: http-status-code
          description: The error's HTTP status code.
          example: 403
    comment404Error:
      title: Not Found
      type: object
      properties:
        type:
          type: string
          format: uri-reference
          description: The [URI reference](https://www.rfc-editor.org/rfc/rfc3986) that identifies the type of problem.
          example: https://api.postman.com/problems/not-found
        title:
          type: string
          description: A short summary of the problem.
          example: Not Found
        detail:
          type: string
          description: Information about the error.
          example: Not Found
        status:
          type: integer
          format: http-status-code
          description: The error's HTTP status code.
          example: 404
    comment500Error:
      title: Internal Server Error
      type: object
      properties:
        type:
          type: string
          format: uri-reference
          description: The [URI reference](https://www.rfc-editor.org/rfc/rfc3986) that identifies the type of problem.
          example: https://api.postman.com/problems/internal-server-error
        title:
          type: string
          description: A short summary of the problem.
          example: Internal Server Error
        detail:
          type: string
          description: Information about the error.
          example: Internal Server Error
        status:
          type: integer
          format: http-status-code
          description: The error's HTTP status code.
          example: 500
    commentCreate:
      title: Comment Create
      type: object
      description: Information about the comment.
      required:
        - body
      properties:
        body:
          type: string
          description: The contents of the comment.
          example: This is an example.
        threadId:
          type: integer
          description: The comment's thread ID. To create a reply on an existing comment, include this property.
          example: 12345
        tags:
          type: object
          description: Information about users tagged in the `body` comment.
          properties:
            userName:
              type: object
              required:
                - type
                - id
              description: An object that contains information about the tagged user. The object's name is the user's Postman username. For example, `@user-postman`.
              properties:
                type:
                  type: string
                  description: The `user` value.
                  enum:
                    - user
                  example: user
                id:
                  type: integer
                  description: The user's ID.
                  example: 87654321
    commentCreatedUpdated:
      title: Comment Updated/Created Object
      type: object
      properties:
        data:
          type: object
          properties:
            id:
              type: integer
              description: The comment's ID.
              example: 46814
            threadId:
              type: integer
              description: The comment's thread ID.
              example: 12345
            createdBy:
              type: integer
              description: The user ID of the user who created the comment.
              example: ********
            createdAt:
              type: string
              format: date-time
              description: The date and time at which the comment was created.
              example: '2024-01-18T11:30:40.000Z'
            updatedAt:
              type: string
              format: date-time
              description: The date and time when the comment was last updated.
              example: '2024-01-18T11:30:40.000Z'
            body:
              type: string
              description: The contents of the comment.
              example: This is an example.
    commentUpdate:
      title: Comment Update
      type: object
      description: Information about the comment.
      required:
        - body
      properties:
        body:
          type: string
          description: The contents of the comment.
          example: This is an example.
        tags:
          type: object
          description: Information about users tagged in the `body` comment.
          properties:
            userName:
              type: object
              required:
                - type
                - id
              description: An object that contains information about the tagged user. The object's name is the user's Postman username. For example, `@user-postman`.
              properties:
                type:
                  type: string
                  description: The `user` value.
                  enum:
                    - user
                  example: user
                id:
                  type: integer
                  description: The user's ID.
                  example: 87654321
    apiCollection400InvalidParam:
      title: Invalid Parameter
      type: object
      properties:
        type:
          type: string
          description: The type of error.
          example: invalidParamsError
        title:
          type: string
          description: A short summary of the problem.
          example: The specified parameter is in an invalid format
        detail:
          type: string
          description: Information about the error.
          example: Parameter, collectionId is in an invalid format.
    apiSchema404ErrorNotFound:
      title: Schema Not Found
      type: object
      properties:
        type:
          type: string
          description: The type of error.
          example: instanceNotFoundError
        title:
          type: string
          description: A short summary of the problem.
          example: Schema not found
        detail:
          type: string
          description: Information about the error.
          example: We could not find a schema linked to this API
    apiSchema400ErrorInvalidParams:
      title: Schema Already Exists
      type: object
      properties:
        type:
          type: string
          description: The type of error.
          example: invalidParamsError
        title:
          type: string
          description: A short summary of the problem.
          example: Could not create the resource
        detail:
          type: string
          description: Information about the error.
          example: Schema already exists for this API
    api404ErrorInstanceNotFound:
      title: API Not Found
      type: object
      properties:
        type:
          type: string
          description: The type of error.
          example: instanceNotFoundError
        title:
          type: string
          description: A short summary of the problem.
          example: Resource not found
        detail:
          type: string
          description: Information about the error.
          example: We could not find the API you are looking for
    apiSchema400ErrorNotLinked:
      title: Schema Not Linked to API
      type: object
      properties:
        type:
          type: string
          description: The error type.
          example: invalidParamError
        title:
          type: string
          description: A short summary of the problem.
          example: Invalid param error
        detail:
          type: string
          description: Details about the error.
          example: Schema is not linked to the API
    apiVersion400ErrorInvalidParam:
      title: Invalid Parameter
      type: object
      properties:
        type:
          type: string
          description: The type of error.
          example: invalidParamsError
        title:
          type: string
          description: A short summary of the problem.
          example: The specified parameter is in an invalid format
        detail:
          type: string
          description: Information about the error.
          example: Parameter, taskId is in an invalid format.
    taskNotFound:
      title: Task Not Found
      type: object
      properties:
        type:
          type: string
          description: The type of error.
          example: invalidParamsError
        title:
          type: string
          description: A short summary of the problem.
          example: The specified parameter is in an invalid format
        detail:
          type: string
          description: Information about the error.
          example: Parameter, taskId is in an invalid format.
    apiVersion404ErrorNotFound:
      title: API Version Not Found
      type: object
      properties:
        type:
          type: string
          description: The type of error.
          example: VersionNotFound
        title:
          type: string
          description: A short summary of the problem.
          example: API Version not found.
        detail:
          type: string
          description: Information about the error.
          example: We could not find the API Version you are looking for.
    apiVersion400ErrorInstanceNotFound:
      title: API Version Not Found
      type: object
      properties:
        error:
          type: object
          properties:
            name:
              type: string
              description: The type of error.
              example: instanceNotFoundError
            message:
              type: string
              description: The error message.
              example: We could not find the API version you are looking for
    sinceDateQuery:
      type: string
      format: date
      example: '2022-08-30'
    untilDateQuery:
      type: string
      format: date
      example: '2022-09-15'
    auditLogsLimitQuery:
      type: integer
      maximum: 300
      example: 50
    ascDescDefaultDesc:
      type: string
      default: desc
      enum:
        - asc
        - desc
      example: desc
    getAuditLogs:
      title: Successful Response
      type: object
      properties:
        trails:
          type: array
          items:
            type: object
            properties:
              id:
                type: number
                description: The audit event's ID.
                example: ********
              ip:
                type: string
                description: The IP address of the user that performed the action.
                example: *********
              userAgent:
                type: string
                description: The user agent information.
                example: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
              action:
                type: string
                description: The action performed by the user.
                example: user.login_google_success
              timestamp:
                type: string
                format: date-time
                description: The date and time at which the event occurred.
                example: '2022-08-31T15:19:32.000Z'
              message:
                type: string
                description: The audit event's description.
                example: Taylor Lee successfully logged in using the Google OAuth.
              data:
                type: object
                properties:
                  actor:
                    type: object
                    description: Information about the user who preformed the audit event.
                    properties:
                      name:
                        type: string
                        description: The user's name.
                        example: Taylor Lee
                      username:
                        type: string
                        description: The user's username.
                        example: taylor-lee
                      email:
                        type: string
                        format: email
                        description: The user's email address.
                        example: <EMAIL>
                      id:
                        type: number
                        example: ********
                      active:
                        type: boolean
                        description: If true, the user is active. If false, the user is deactivated.
                        example: true
                  user:
                    type: object
                    description: Information about the user.
                    properties:
                      name:
                        type: string
                        description: The user's name.
                        example: Taylor Lee
                      username:
                        type: string
                        description: The user's username.
                        example: taylor-lee
                      email:
                        type: string
                        format: email
                        description: The user's email address.
                        example: <EMAIL>
                      id:
                        type: number
                        description: The user's ID.
                        example: ********
                  team:
                    type: object
                    description: The user's team information.
                    properties:
                      name:
                        type: string
                        description: The team's name.
                        example: Test-Team
                      id:
                        type: number
                        description: The team's ID.
                        example: 1234
                  variables:
                    type: object
                    description: Additional information about the performed action.
                    additionalProperties: true
    getCollectionAccessKeys:
      title: Collection Access Keys
      type: object
      properties:
        data:
          type: array
          description: A list of collection access keys.
          items:
            type: object
            description: Information about the collection access key.
            properties:
              id:
                type: string
                description: The collection access key's ID.
                example: Njg5OjU3MDQ1NjYtYmQxZDU3NzktMWVkNS00ZDhjLWI0ZmQtZWRhOGY2Mzg1NTY0
              token:
                type: string
                description: The masked collection access key.
                example: PMAT-**********************43BR
              status:
                type: string
                description: The token's active status.
                enum:
                  - ACTIVE
                  - INACTIVE
                example: ACTIVE
              teamId:
                type: integer
                description: The team ID of the collection access key's owner.
                example: 123
              userId:
                type: integer
                description: The user ID of the collection access key's owner.
                example: ********
              collectionId:
                type: string
                format: uid
                description: The collection access key's associated collection ID.
                example: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
              expiresAfter:
                type: string
                format: date-time
                description: The date and time at which the collection access key expires. Collection access keys are valid for 60 days. If unused, the access key expires after 60 days. If someone uses the access key, then its expiration date increases by another 60 days.
                example: '2024-06-11T13:21:11.000Z'
              lastUsedAt:
                type: string
                description: The date and time at which the collection access key was used. If the collection key is unused, this returns an empty string value.
                example: ''
              createdAt:
                type: string
                format: date-time
                example: '2024-04-12T13:21:11.000Z'
                description: The date and time at which the collection access key was created.
              updatedAt:
                type: string
                format: date-time
                description: The date and time at which the collection access key was updated.
                example: '2024-04-12T13:21:11.000Z'
              deletedAt:
                type: string
                format: date-time
                nullable: true
                description: The date and time at which the collection access key was revoked.
                example: '2024-04-12T13:21:11.000Z'
        meta:
          type: object
          description: The response's non-standard metadata information.
          properties:
            nextCursor:
              type: string
              description: The pagination cursor that points to the next record in the results set.
              example: b2Zmc2V0PTEwJmxpbWl0PTEw
            prevCursor:
              type: string
              description: The pagination cursor to previous set of response data.
              example: ''
    cakNotFound404Error:
      title: Key Not Found
      type: object
      properties:
        type:
          type: string
          format: uri-reference
          description: The [URI reference](https://www.rfc-editor.org/rfc/rfc3986) that identifies the type of problem.
          example: https://api.postman.com/problems/not-found
        title:
          type: string
          description: A short summary of the problem.
          example: Not Found
        status:
          type: integer
          format: http-status-code
          description: The error's HTTP status code.
          example: 404
        detail:
          type: string
          description: Information about the error.
          example: The key was not found
    asyncMergeCollectionFork:
      title: Successful Response
      type: object
      properties:
        task:
          type: object
          description: Information about the merge task.
          properties:
            id:
              type: string
              description: The task's ID.
              example: pm~vc:merge-workflow~********-4824989b-cd77-4e2f-856c-1ff2a3e9aaca
            status:
              type: string
              description: The task's status.
              example: in-progress
    collectionForkTaskId:
      type: string
      example: pm~vc:merge-workflow~********-5a027c05-056b-49f5-ac9d-37c29f2d91c3
    asyncMergePullCollectionTaskStatus:
      title: Task Status Response
      type: object
      properties:
        id:
          type: string
          description: The task's ID.
          example: pm~vc:merge-workflow~********-5a027c05-056b-49f5-ac9d-37c29f2d91c3
        status:
          type: string
          description: The task's current status.
          enum:
            - successful
            - in-progress
            - failed
          example: successful
        details:
          type: object
          description: If the task failed, an object containing information about the error.
          properties:
            error:
              type: object
              properties:
                message:
                  type: string
                  description: The error message.
                  example: All conflicts have not be resolved
    limitNoDefault:
      type: integer
      example: 3
    offsetNoDefault:
      type: integer
      example: 0
    metaLimitOffsetTotal:
      type: object
      description: The response's meta information for paginated results.
      properties:
        total:
          type: number
          description: The number of records found.
          example: 30
        offset:
          type: number
          description: The zero-based offset of the first item returned.
          example: 0
        limit:
          type: number
          description: The maximum number of records in the paginated response.
          example: 3
    getCollections:
      title: Collections List
      type: object
      properties:
        collections:
          type: array
          items:
            type: object
            description: Information about the collection.
            properties:
              id:
                type: string
                description: The collection's ID.
                example: 12ece9e1-2abf-4edc-8e34-de66e74114d2
              name:
                type: string
                description: The collection's name.
                example: Test Collection
              owner:
                type: string
                description: The owner of the collection.
                example: '********'
              createdAt:
                type: string
                format: date-time
                description: The collection's creation date and time.
                example: '2022-01-13T10:21:46.000Z'
              updatedAt:
                type: string
                format: date-time
                description: The date and time at which the collection was last updated.
                example: '2022-01-13T10:21:46.000Z'
              uid:
                type: string
                format: uid
                description: The collection's unique ID.
                example: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
              fork:
                type: object
                description: If the collection is [forked](https://learning.postman.com/docs/collaborating-in-postman/version-control/#forking-postman-entities), the fork's information.
                properties:
                  label:
                    type: string
                    description: The fork's label.
                    example: Test Fork
                  createdAt:
                    type: string
                    format: date-time
                    description: The fork's creation date and time.
                    example: '2022-06-16T19:51:44.069Z'
                  from:
                    type: string
                    description: The unique ID of the fork's source collection.
                    example: ********-f2f0cb8f-609d-443f-913c-9831187c326e
              isPublic:
                type: boolean
                description: If true, the collection is public and visible to all users.
                example: false
        meta:
          $ref: '#/components/schemas/metaLimitOffsetTotal'
    ascDesc:
      type: string
      enum:
        - asc
        - desc
      example: asc
    fork400ErrorNoUserFound:
      title: Bad Request
      type: object
      properties:
        error:
          type: object
          properties:
            name:
              type: string
              description: The error name.
              example: badRequest
            message:
              type: string
              description: The error message.
              example: No Postman User found for given filters
    getCollection:
      title: Collection Information
      type: object
      properties:
        collection:
          type: object
          additionalProperties: true
          description: For a complete list of this endpoint's possible values, use the [Postman Collection Format documentation](https://schema.postman.com/collection/json/v2.1.0/draft-07/docs/index.html).
          properties:
            info:
              type: object
              description: An object that contains basic information about the collection.
              properties:
                _postman_id:
                  type: string
                  description: The collection's Postman ID.
                  example: 12ece9e1-2abf-4edc-8e34-de66e74114d2
                name:
                  type: string
                  description: The collection's name.
                  example: Test Collection
                description:
                  type: string
                  description: The collection's description.
                  example: This is a sample collection that makes a tiny request to Postman Echo service to get the list of request headers sent by a HTTP client.
                schema:
                  type: string
                  format: url
                  description: A URL to the collection's schema.
                  example: https://schema.getpostman.com/json/collection/v2.1.0/collection.json
                fork:
                  type: object
                  description: If the collection was forked from another collection, this object contains information about the fork.
                  properties:
                    label:
                      type: string
                      description: The fork's label.
                      example: Collection fork
                    createdAt:
                      type: string
                      format: date-time
                      description: The date and time at which the collection was forked.
                      example: '2024-07-17T13:12:43.000Z'
                    from:
                      type: string
                      format: uid
                      description: The source (parent) collection's unique ID.
                      example: 87654321-c8142d52-f97d-46a7-bc77-52bb99713o1n
                updatedAt:
                  type: string
                  format: date-time
                  description: The date and time at which the collection was last updated.
                  example: '2023-10-13T08:14:22.000Z'
                uid:
                  type: string
                  format: uid
                  description: The collection's unique ID.
                  example: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
                createdAt:
                  type: string
                  format: date-time
                  description: The date and time at which the collection was created.
                  example: '2023-10-08T13:04:28.000Z'
                lastUpdatedBy:
                  type: string
                  description: The user ID of the user who last updated the collection.
                  example: '********'
            item:
              type: array
              items:
                type: object
                additionalProperties: true
                description: The collection's request information. For a complete list of values, refer to the **Request** entry in the [Postman Collection Format documentation](https://schema.postman.com/collection/json/v2.1.0/draft-07/docs/index.html). If you pass an empty object for this value, the system defaults to an untitled GET request.
    common403Error:
      title: Forbidden
      type: object
      properties:
        type:
          type: string
          format: uri-reference
          description: The [URI reference](https://www.rfc-editor.org/rfc/rfc3986) that identifies the type of problem.
          example: https://api.postman.com/problems/forbidden
        title:
          type: string
          description: A short summary of the problem.
          example: Forbidden
        detail:
          type: string
          description: Information about the error.
          example: Forbidden
        status:
          type: integer
          format: http-status-code
          description: The error's HTTP status code.
          example: 403
    fork404Error:
      title: Not Found
      type: object
      properties:
        error:
          type: object
          properties:
            name:
              type: string
              description: The error name.
              example: notFoundError
            message:
              type: string
              description: The error message.
              example: Fork not found.
    pullRequest403ErrorRolePermissionsCollection:
      title: No Role Permissions
      type: object
      properties:
        detail:
          type: string
          description: Details about the occurrence of the error.
          example: You need Viewer role on both source and destination collections to view pull requests. Please contact collection editors to grant additional access to you.
        status:
          type: number
          format: http-status-code
          description: The error's HTTP status code.
          example: 403
        title:
          type: string
          description: A short summary of the error.
          example: Forbidden
        type:
          type: string
          format: uri
          description: The error type.
          example: https://api.postman.com/problems/forbidden
    pullRequestCreate:
      title: Create Pull Request
      type: object
      required:
        - title
        - description
        - reviewers
        - destinationId
      description: Information about the pull request.
      properties:
        title:
          type: string
          description: The title of the pull request.
          example: Test PR
        description:
          type: string
          description: The pull request's description.
          example: This is a test pull request.
        reviewers:
          type: array
          description: A list of reviewers to assign to the pull request.
          items:
            type: string
            description: The reviewer's user ID.
            example: '87654321'
        destinationId:
          type: string
          description: The collection ID to merge the pull request into.
          example: ********-ec548788-unftw-rgn8-83b8-0b59798648e4
    pullRequestCreated:
      title: Pull Request Created
      type: object
      properties:
        createdAt:
          type: string
          description: The date and time at which the pull request was created.
          example: '2024-02-20T09:55:46.000Z'
        createdBy:
          type: string
          description: The ID of the user who created the pull request.
          example: '********'
        description:
          type: string
          description: The pull request's description.
          example: This is a test pull request.
        destinationId:
          type: string
          description: The pull request's merge destination ID.
          example: ********-ec548788-unftw-rgn8-83b8-0b59798648e4
        id:
          type: string
          description: The pull request's ID.
          example: 4e1a6609-1a29-4037-a411-89ecc14c6cd8
        sourceId:
          type: string
          description: The pull request's source (parent) ID.
          example: 87654321-3b79068c-dbe5-41d5-a826-51be4bf646ef
        status:
          type: string
          description: The pull request's current status.
          example: open
        title:
          type: string
          description: The pull request's title.
          example: Test PR
        updatedAt:
          type: string
          description: The date and time at which the pull request was updated.
          example: '2024-02-20T09:58:57.000Z'
    forkCollection400ErrorBadForkRelation:
      title: Invalid Fork Source
      type: object
      properties:
        type:
          type: string
          format: uri-reference
          description: The [URI reference](https://www.rfc-editor.org/rfc/rfc3986) that identifies the type of problem.
          example: https://api.postman.com/problems/bad-request
        title:
          type: string
          description: A short summary of the problem.
          example: Bad Request
        status:
          type: number
          format: http-status-code
          description: The error's HTTP status code.
          example: 400
        detail:
          type: string
          description: Information about the error.
          example: Fork relation not found
    pullRequest400ErrorDuplicate:
      title: Pull Request Already Exists
      type: object
      properties:
        detail:
          type: string
          description: Details about the occurrence of the error.
          example: A pull request for this source and destination already exists.
        status:
          type: number
          format: http-status-code
          description: The error's HTTP status code.
          example: 400
        title:
          type: string
          description: A short summary of the error.
          example: Duplicate pull request
        type:
          type: string
          format: uri
          description: The error type.
          example: https://api.postman.com/problems/bad-request
    pullRequest403ErrorNoViewerAccessCollections:
      title: No Viewer Permissions
      type: object
      properties:
        detail:
          type: string
          description: Details about the occurrence of the error.
          example: You and all reviewers need Viewer access on both source and destination collections.
        status:
          type: number
          format: http-status-code
          description: The error's HTTP status code.
          example: 403
        title:
          type: string
          description: A short summary of the error.
          example: Forbidden
        type:
          type: string
          format: uri
          description: The error type.
          example: https://api.postman.com/problems/forbidden
    collectionFolderId:
      type: string
      example: 65a99e60-8e0a-4b6e-b79c-7d8264cc5caa
    collectionRequestId:
      type: string
      example: c82dd02c-4870-4907-8fcb-593a876cf05b
    pullRequest403ErrorForbidden:
      title: Forbidden
      type: object
      properties:
        detail:
          type: string
          description: Details about the occurrence of the error.
          example: You do not have enough permissions to perform this action.
        status:
          type: number
          format: http-status-code
          description: The error's HTTP status code.
          example: 403
        title:
          type: string
          description: A short summary of the error.
          example: Forbidden
        type:
          type: string
          format: uri
          description: The error type.
          example: https://api.postman.com/problems/forbidden
    booleanQuery:
      type: boolean
      example: true
    transferCollectionItems:
      title: Transfer Items Payload
      type: object
      required:
        - ids
        - target
        - location
        - mode
      properties:
        ids:
          type: array
          description: A list of collection request, response, or folder UIDs to transfer.
          items:
            type: string
            format: uid
            example: ********-a9b481c1-3e78-4af7-8db0-dce3f3f3c105
        mode:
          type: string
          description: The transfer operation to perform.
          enum:
            - copy
            - move
          example: move
        target:
          type: object
          description: Information about the item transfer's destination location.
          required:
            - model
            - id
          properties:
            id:
              type: string
              format: uid
              example: ********-b91270fa-048d-4f5f-a033-8b5523bf053f
              description: The UID of the destination collection, folder, or request.
            model:
              type: string
              description: The collection, folder, or request the items will be transferred to. For response transfers, use the `request` value.
              enum:
                - collection
                - folder
                - request
              example: request
        location:
          type: object
          description: |
            The transferred items' placement in the target destination:
            - For `start` or `end` — Do not include the `model` and `id` values.
            - For `before` or `after` — Include the `model` and `id` properties.
          required:
            - position
          properties:
            id:
              type: string
              nullable: true
              format: uid
              description: For `before` or `after` positions, the model's UID.
              example: ********-80812b16-ac27-45b3-b3eb-793f78530d32
            model:
              type: string
              nullable: true
              description: |
                For `before` or `after` positions, the type of item (model) that the transferred item will be positioned by. One of: `folder`, `request`, or `response.`
              example: response
            position:
              type: string
              default: end
              description: The transferred item's position within the destination object.
              enum:
                - start
                - end
                - before
                - after
              example: before
    include:
      type: string
      example: meta.total
    sinceDateTime:
      type: string
      format: date-time
      example: '2022-06-01T00:00:00.000Z'
    untilDateTime:
      type: string
      format: date-time
      example: '2022-06-15T00:00:00.000Z'
    detectedSecretsQueriesRequest:
      title: Detected Secrets Query Request
      type: object
      properties:
        resolved:
          type: boolean
          description: If true, return secrets with a `resolved` status.
          example: true
        secretTypes:
          type: array
          description: A list of secrets types to query. For a list of valid IDs, use the GET `/secret-types` endpoint.
          items:
            type: string
            description: The secret type ID.
            example: 1a7ec5d1-dcba-4ec7-8220-3c1ee490416b
        statuses:
          type: array
          description: A list of the types of resolution statuses to query.
          items:
            type: string
            description: |
              The secret resolution status type:
              - `FALSE_POSITIVE` — The discovered secret is not an actual secret.
              - `REVOKED` — The secret is valid, but the user rotated their key to resolve the issue.
              - `ACCEPTED_RISK` — The Secret Scanner found the secret, but user accepts the risk of publishing it.
            enum:
              - FALSE_POSITIVE
              - ACCEPTED_RISK
              - REVOKED
            example: ACCEPTED_RISK
        resources:
          type: array
          description: A list of resources to query. If you use this query, you cannot also pass the `workspaceIds` query.
          items:
            type: object
            properties:
              type:
                type: string
                description: The type of resource.
                enum:
                  - collection
                  - environment
                  - extensible-collection
                  - globals
                  - example
                  - request
                  - folder
                  - extensible-collection-meta
                  - extensible-request
                  - extensible-folder
                  - extensible-example
                  - extensible-message
                  - api-definition-file
                  - api-definition
                example: collection
              ids:
                type: array
                description: A list of resource IDs.
                items:
                  type: string
                  description: The resource's ID.
                  example: ********-94720289-b9f3-4572-b34d-ecdfb9225b07
        workspaceIds:
          type: array
          description: A list of workspaces IDs to query. If you use this query, you cannot also pass the `resources` query.
          items:
            type: string
            description: The workspace ID.
            example: 0fe6c2f2-022d-48f7-8e7e-3244369445b0
        workspaceVisibilities:
          type: array
          description: A list of workspace [visibility settings](https://learning.postman.com/docs/collaborating-in-postman/using-workspaces/managing-workspaces/#changing-workspace-visibility) to query. This currently supports the `team` and `public` settings.
          items:
            type: string
            description: The type of visibility setting.
            enum:
              - team
              - public
            example: team
    detectedSecretsQueries:
      title: Search Detected Secrets Request
      type: object
      properties:
        meta:
          type: object
          description: The response's meta information for paginated results.
          properties:
            limit:
              type: number
              description: The maximum number of records in the paginated response.
              example: 2
            nextCursor:
              type: string
              nullable: true
              format: base64
              description: The pagination cursor that points to the next record in the results set.
              example: MTA4
            total:
              type: number
              description: The number of records that match the defined criteria. This is only returned when the `include` query parameter is passed with the `meta.total` value.
              example: 20
        data:
          type: array
          items:
            type: object
            properties:
              detectedAt:
                type: string
                format: date-time
                example: '2023-05-19T02:45:31.000Z'
                description: The date and time at which the secret was first detected.
              secretType:
                type: string
                description: The type of the secret.
                example: Airtable API Key
              workspaceVisibility:
                type: string
                description: The workspace's [visibility setting](https://learning.postman.com/docs/collaborating-in-postman/using-workspaces/managing-workspaces/#changing-workspace-visibility).
                enum:
                  - personal
                  - private
                  - team
                  - public
                example: team
              secretHash:
                type: string
                description: The SHA-256 hash of the detected secret.
                example: 07afd1f787f3555d441b04870dbe1025db8309fbeb31f25b3a20f2f1241478b3
              workspaceId:
                type: string
                description: The ID of the workspace that contains the secret.
                example: e361eeb4-00dd-4225-9774-6146a2555999
              resourceType:
                type: string
                description: If querying by resource, the resource type.
                example: example
              resourceId:
                type: string
                description: If querying by resource, the resource's ID.
                example: ********-400005df-3cad-480c-a027-ea68aed35b60
              secretId:
                type: string
                description: The detected secret's ID.
                example: OTI3OTYx
              obfuscatedSecret:
                type: string
                description: The secret's obfuscated value.
                example: keyTF2WAH******
              resolution:
                type: string
                description: |
                  The secret's current status:
                  - `ACTIVE` — The secret is active.
                  - `FALSE_POSITIVE` — The discovered secret is not an actual secret.
                  - `REVOKED` — The secret is valid, but the user rotated their key to resolve the issue.
                  - `ACCEPTED_RISK` — The Secret Scanner found the secret, but user accepts the risk of publishing it.
                enum:
                  - FALSE_POSITIVE
                  - ACCEPTED_RISK
                  - REVOKED
                  - ACTIVE
                example: FALSE_POSITIVE
              occurrences:
                type: number
                description: The number of times the secret was found in the workspace.
                example: 3
    secretScanner400Error:
      title: Bad Request
      type: object
      properties:
        instance:
          type: string
          description: The URI reference that identifies the specific occurrence of the problem.
          example: ''
        status:
          type: number
          format: http-status-code
          description: The error's HTTP status code.
          example: 400
        title:
          type: string
          description: A short summary of the problem.
          example: limit specified is invalid
        type:
          type: string
          description: The type of error.
          example: BAD_REQUEST
    detectedSecret400Error:
      title: Invalid Filter
      type: object
      properties:
        type:
          type: string
          description: The type of error.
          example: BAD_REQUEST
        title:
          type: string
          description: A short summary of the problem.
          example: Invalid filters specified
        instance:
          type: string
          description: The URI reference that identifies the specific occurrence of the problem.
          example: ''
        status:
          type: number
          format: http-status-code
          description: The error's HTTP status code.
          example: 400
        detail:
          type: string
          description: Information about the error.
          example: '`workspaceIds` and `resources` filters are exclusive - only one can be applied at a time.'
    secretScanner401Error:
      title: Unauthorized
      type: object
      properties:
        instance:
          type: string
          description: The URI reference that identifies the specific occurrence of the problem.
          example: ''
        status:
          type: number
          format: http-status-code
          description: The error's HTTP status code.
          example: 401
        title:
          type: string
          description: A short summary of the problem.
          example: Unauthorized. Please try again with valid credentials.
        type:
          type: string
          description: The type of error.
          example: UNAUTHORIZED
    secretScanner403Error:
      title: Forbidden
      type: object
      properties:
        instance:
          type: string
          description: The URI reference that identifies the specific occurrence of the problem.
          example: ''
        status:
          type: number
          format: http-status-code
          description: The error's HTTP status code.
          example: 403
        title:
          type: string
          description: A short summary of the problem.
          example: Only members with Superadmin, Admin, or Workspace Admin roles can view the detected secrets.
        type:
          type: string
          description: The type of error.
          example: FORBIDDEN
    secretScanner500Error:
      title: Internal Server Error
      type: object
      properties:
        instance:
          type: string
          description: The URI reference that identifies the specific occurrence of the problem.
          example: ''
        status:
          type: number
          format: http-status-code
          description: The error's HTTP status code.
          example: 500
        title:
          type: string
          description: A short summary of the problem.
          example: An error occurred
        type:
          type: string
          description: The type of error.
          example: SERVER_ERROR
    secretId:
      type: string
      example: MTc0ODA0Mw==
    updateSecretResolutionsRequest:
      title: Update Secret Resolution Request
      required:
        - resolution
        - workspaceId
      type: object
      properties:
        resolution:
          type: string
          description: |
            The secret's updated resolution status:
            - `FALSE_POSITIVE` — The discovered secret is not an actual secret.
            - `REVOKED` — The secret is valid, but the user rotated their key to resolve the issue.
            - `ACCEPTED_RISK` — The Secret Scanner found the secret, but user accepts the risk of publishing it.
          enum:
            - FALSE_POSITIVE
            - REVOKED
            - ACCEPTED_RISK
          example: ACCEPTED_RISK
        workspaceId:
          type: string
          description: The ID of the workspace that contains the secret.
          example: e361eeb4-00dd-4225-9774-6146a2555999
    updateSecretResolutions:
      title: Successful Response
      type: object
      properties:
        secretHash:
          type: string
          description: The SHA-256 hash of the detected secret.
          example: 50dbd2...
        workspaceId:
          type: string
          description: The ID of the workspace that contains the secret.
          example: 1f0df51a-8658-4ee8-a2a1-d2567dfa09a9
        resolution:
          type: string
          description: |
            The secret's current resolution status:
            - `ACTIVE` — The secret is active.
            - `FALSE_POSITIVE` — The discovered secret is not an actual secret.
            - `REVOKED` — The secret is valid, but the user rotated their key to resolve the issue.
            - `ACCEPTED_RISK` — The Secret Scanner found the secret, but user accepts the risk of publishing it.
          example: FALSE_POSITIVE
          enum:
            - FALSE_POSITIVE
            - ACCEPTED_RISK
            - REVOKED
            - ACTIVE
        history:
          type: array
          description: The history of the secret's resolution status changes.
          items:
            type: object
            properties:
              actor:
                type: number
                description: The ID of the user that updated the secret's resolution status.
                example: ********
              createdAt:
                type: string
                format: date-time
                description: The date and time at which the resolution status was updated.
                example: '2024-09-18T18:46:26.000Z'
              resolution:
                type: string
                description: |
                  The secret's updated resolution status:
                  - `ACTIVE` — The secret is active.
                  - `FALSE_POSITIVE` — The discovered secret is not an actual secret.
                  - `REVOKED` — The secret is valid, but the user rotated their key to resolve the issue.
                  - `ACCEPTED_RISK` — The Secret Scanner found the secret, but user accepts the risk of publishing it.
                enum:
                  - FALSE_POSITIVE
                  - ACCEPTED_RISK
                  - REVOKED
                  - ACTIVE
                example: FALSE_POSITIVE
    resourceType:
      type: string
      enum:
        - collection
        - environment
        - extensible-collection
        - globals
        - example
        - request
        - folder
        - extensible-collection-meta
        - extensible-request
        - extensible-folder
        - extensible-example
        - extensible-message
        - api-definition-file
        - api-definition
      example: collection
    getSecretsLocations:
      title: Successful Response
      type: object
      properties:
        data:
          type: array
          items:
            type: object
            description: Information about the secret finding locations.
            properties:
              isResourceDeleted:
                type: boolean
                description: If true, the resource in which the secret was found was deleted.
                example: false
              leakedBy:
                type: integer
                description: The ID of the user who exposed the secret.
                example: ********
              location:
                type: string
                description: The location where the secret was found.
                example: Headers
              occurrences:
                type: integer
                description: The number of times the secret occurs in the location.
                example: 1
              parentResourceId:
                type: string
                format: uid
                description: The parent resource's unique ID. If the resource is a request, folder, or example, this value is a collection ID. If the resource is a collection, globals, or environment, this is the resource's ID.
                example: ********-14728df4-c7af-424f-b665-5047b7d25866
              resourceId:
                type: string
                format: uid
                description: The unique ID of the resource where the secret was detected.
                example: ********-e0df25e0-d245-40dd-89b5-68720d186d3f
              resourceType:
                type: string
                description: The type of resource in which the secret was detected.
                enum:
                  - collection
                  - folder
                  - request
                  - example
                  - environment
                  - globals
                  - api
                example: example
              detectedAt:
                type: string
                format: date-time
                description: The date and time at which the secret was detected.
                example: '2023-04-25T08:13:48.000Z'
              url:
                type: string
                description: The URL to the resource that contains the secret.
                example: https://go.postman.co/build/workspace/80ab14ae-c17d-4fd6-88d5-99bf13f0b7f0/example/********-e0df25e0-d245-40dd-89b5-68720d186d3f
        meta:
          type: object
          properties:
            activityFeed:
              type: array
              description: The history of the secret's resolution status changes.
              items:
                type: object
                properties:
                  resolvedAt:
                    type: string
                    format: date-time
                    description: The date and time at which the resolution status was last updated.
                    example: '2023-04-25T11:18:07.000Z'
                  resolvedBy:
                    type: integer
                    description: The ID of the user that updated the secret's resolution status.
                    example: ********
                  status:
                    type: string
                    description: |
                      The secret's current resolution status:
                      - `ACTIVE` — The secret is active.
                      - `FALSE_POSITIVE` — The discovered secret is not an actual secret.
                      - `REVOKED` — The secret is valid, but the user rotated their key to resolve the issue.
                      - `ACCEPTED_RISK` — The Secret Scanner found the secret, but user accepts the risk of publishing it.
                    enum:
                      - FALSE_POSITIVE
                      - ACCEPTED_RISK
                      - REVOKED
                      - ACTIVE
                    example: FALSE_POSITIVE
            cursor:
              type: string
              description: The pointer to the first record of the set of paginated results.
              example: MTIyNjY5Nw==
            limit:
              type: integer
              description: The maximum number of rows to return in the response.
              example: 2
            nextCursor:
              type: string
              nullable: true
              description: The Base64-encoded value that points to the next record in the results set.
              example: null
            obfuscatedSecret:
              type: string
              description: The secret's obfuscated value.
              example: PMAK-644781584627df042afa6655-******ba
            secretHash:
              type: string
              description: The secret's SHA-256 hash.
              example: 0096b35ef6621d7571f106fefee5b10e8ed360cc9bf04f343f267ca4ff65bb5d
            secretType:
              type: string
              description: The type of thesecret.
              example: Postman API Key
            total:
              type: integer
              description: The total number of discovered secret locations.
              example: 3
    getEnvironments:
      title: Successful Response
      type: object
      properties:
        environments:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
                description: The environment's ID.
                example: 5daabc50-8451-43f6-922d-96b403b4f28e
              name:
                type: string
                description: The environment's name.
                example: Test Environment
              createdAt:
                type: string
                format: date-time
                description: The date and time at which the environment was created.
                example: '2020-09-23T14:31:18.000Z'
              updatedAt:
                type: string
                format: date-time
                description: The date and time at which the environment was last updated.
                example: '2020-12-04T14:13:40.000Z'
              owner:
                type: string
                description: The environment owner's ID.
                example: '********'
              uid:
                type: string
                format: uid
                description: The environment's unique ID.
                example: ********-5daabc50-8451-43f6-922d-96b403b4f28e
              isPublic:
                type: boolean
                description: If true, the environment is public and visible to all users.
                example: false
    instanceNotFoundEnvironment:
      title: Instance Not Found
      type: object
      properties:
        error:
          type: object
          properties:
            name:
              type: string
              description: The error name.
              example: instanceNotFoundError
            message:
              type: string
              description: The error message.
              example: We could not find the environment you are looking for
    createEnvironment:
      title: Create Environment
      type: object
      properties:
        environment:
          type: object
          required:
            - name
          properties:
            name:
              type: string
              description: The environment's name.
              example: Test Environment
            values:
              type: array
              description: Information about the environment's variables.
              items:
                type: object
                properties:
                  enabled:
                    type: boolean
                    description: If true, the variable is enabled.
                    example: true
                  key:
                    type: string
                    description: The variable's name.
                    example: collectionId
                  value:
                    type: string
                    description: The variable's value.
                    example: 12ece9e1-2abf-4edc-8e34-de66e74114d2
                  type:
                    type: string
                    description: The variable type.
                    enum:
                      - secret
                      - default
                      - any
                    example: default
    createEnvironmentResponse:
      title: Environment Created
      type: object
      properties:
        environment:
          type: object
          properties:
            id:
              type: string
              description: The environment's ID.
              example: 5daabc50-8451-43f6-922d-96b403b4f28e
            name:
              type: string
              description: The environment's name.
              example: Test Environment
            uid:
              type: string
              format: uid
              description: The environment's unique ID.
              example: ********-5daabc50-8451-43f6-922d-96b403b4f28e
    environments400ErrorMalformedRequest:
      title: Bad Request
      type: object
      properties:
        error:
          type: object
          properties:
            name:
              type: string
              description: The error name.
              example: malformedRequestError
            message:
              type: string
              description: The error message.
              example: Found 1 errors with the supplied environment.
            details:
              type: array
              description: Details about the occurrence of the error.
              items:
                type: string
                example: ': must have required property ''environment'''
    getEnvironment:
      title: Successful Response
      type: object
      properties:
        environment:
          type: object
          properties:
            id:
              type: string
              description: The environment's ID.
              example: 5daabc50-8451-43f6-922d-96b403b4f28e
            name:
              type: string
              description: The environment's name.
              example: Test Environment
            owner:
              type: string
              description: The ID of environment's owner.
              example: '********'
            createdAt:
              type: string
              format: date-time
              description: The date and time at which the environment was created.
              example: '2020-11-05T13:59:22.000Z'
            updatedAt:
              type: string
              format: date-time
              description: The date and time at which the environment was last updated.
              example: '2020-11-05T13:59:23.000Z'
            values:
              type: array
              description: Information about the environment's variables.
              items:
                type: object
                properties:
                  enabled:
                    type: boolean
                    description: If true, the variable is enabled.
                    example: true
                  key:
                    type: string
                    description: The variable's name.
                    example: collectionId
                  value:
                    type: string
                    description: The variable's value.
                    example: 12ece9e1-2abf-4edc-8e34-de66e74114d2
                  type:
                    type: string
                    description: The variable type.
                    enum:
                      - secret
                      - default
                      - any
                    example: default
            isPublic:
              type: boolean
              description: If true, the environment is public and visible to all users.
              example: false
    updateEnvironment:
      title: Update Environment
      type: object
      properties:
        environment:
          type: object
          properties:
            name:
              type: string
              description: The environment's name.
              example: Test Environment
            values:
              type: array
              description: Information about the environment's variables.
              items:
                type: object
                properties:
                  enabled:
                    type: boolean
                    description: If true, the variable is enabled.
                    example: true
                  key:
                    type: string
                    description: The variable's name.
                    example: apiKey
                  value:
                    type: string
                    description: The variable's value.
                    example: PMAK-XXX
                  type:
                    type: string
                    description: The variable type.
                    enum:
                      - secret
                      - default
                      - any
                    example: secret
    updateEnvironmentResponse:
      title: Environment Updated
      type: object
      properties:
        environment:
          type: object
          properties:
            id:
              type: string
              description: The environment's ID.
              example: 5daabc50-8451-43f6-922d-96b403b4f28e
            name:
              type: string
              description: The environment's name.
              example: Test A Environment
            uid:
              type: string
              format: uid
              description: The environment's unique ID.
              example: ********-5daabc50-8451-43f6-922d-96b403b4f28e
    deleteEnvironment:
      title: Environment Deleted
      type: object
      properties:
        environment:
          type: object
          properties:
            id:
              type: string
              description: The deleted environment's ID.
              example: 4dfb28a4-9a6c-4ce4-b31a-17c26a8b2cce
            uid:
              type: string
              format: uid
              description: The deleted environment's unique ID.
              example: ********-4dfb28a4-9a6c-4ce4-b31a-17c26a8b2cce
    getEnvironmentForks:
      title: Successful Response
      type: object
      properties:
        data:
          type: array
          description: A list of the environment's forks.
          items:
            type: object
            description: Information about the forked environment.
            properties:
              forkId:
                type: string
                format: uid
                description: The forked environment's unique ID.
                example: ********-2fgh78uj-2ee3-8ugc-b678-7742d82e2e1f
              forkName:
                type: string
                description: The forked environment's label.
                example: my fork
              createdAt:
                type: string
                format: date-time
                description: The date and time at which the fork was created.
                example: '2023-11-16T09:18:17.000Z'
              createdBy:
                type: string
                description: The user who created the environment fork.
                example: Taylor Lee
              updatedAt:
                type: string
                format: date-time
                description: The date and time at which the fork was last updated.
                example: '2023-11-16T09:18:17.000Z'
        meta:
          type: object
          description: The response's meta information for paginated results.
          properties:
            total:
              type: number
              description: The total number of forked environments.
              example: 6
            nextCursor:
              type: string
              nullable: true
              description: The pagination cursor that points to the next record in the results set.
              example: null
    environmentForks400Error:
      title: Bad Request
      type: object
      properties:
        type:
          type: string
          description: The type of error.
          example: https://api.postman.com/problems/bad-request
        title:
          type: string
          description: A short summary of the error.
          example: The environment Id is not a forked environment.
        detail:
          type: object
          description: Details about the occurrence of the error.
          example: {}
    environmentForks404Error:
      title: Not Found
      type: object
      properties:
        title:
          type: string
          description: A short summary of the error.
          example: Not Found
        status:
          type: number
          format: http-status-code
          description: The error's HTTP status code.
          example: 404
        detail:
          type: string
          description: Details about the occurrence of the error.
          example: We could not find the environment you are looking for
        type:
          type: string
          format: uri
          description: The error type.
          example: https://api.postman.com/problems/notFound
    forkEnvironment:
      title: Fork Environment
      type: object
      required:
        - forkName
      properties:
        forkName:
          type: string
          description: The forked environment's label.
          example: My fork
    forkEnvironmentResponse:
      title: Successful Response
      type: object
      properties:
        environment:
          type: object
          description: Information about the forked environment.
          properties:
            uid:
              type: string
              format: uid
              description: The forked environment's ID.
              example: ********-0fcea3ba-abcf-49c3-9f48-669c208fef25
            name:
              type: string
              description: The name of the forked environment.
              example: Testing Environment
            forkName:
              type: string
              description: The forked environment's label.
              example: Test Fork
    mergeEnvironmentFork:
      title: Merge Environment Fork
      type: object
      properties:
        source:
          type: string
          format: uid
          description: The source environment's unique ID to merge data from.
          example: ********-d9c7dc8f-904e-4bba-99b5-4d490aae1957
        deleteSource:
          type: boolean
          default: false
          description: If true, the forked environment will be deleted.
          example: false
    mergeEnvironmentForkResponse:
      title: Successful Response
      type: object
      properties:
        environment:
          type: object
          description: Information about the merged environment.
          properties:
            uid:
              type: string
              format: uid
              description: The merged environment's ID.
              example: ********-d9c7dc8f-904e-4bba-99b5-4d490aae1957
    pullEnvironment:
      title: Pull Environment Fork Changes
      type: object
      properties:
        source:
          type: string
          format: uid
          description: The source environment's unique ID to pull data from.
          example: ********-d9c7dc8f-904e-4bba-99b5-4d490aae1957
    pullEnvironmentResponse:
      title: Successful Response
      type: object
      properties:
        environment:
          type: object
          description: Information about the merged environment.
          properties:
            uid:
              type: string
              format: uid
              description: The merged environment's unique ID.
              example: ********-d9c7dc8f-904e-4bba-99b5-4d490aae1957
    getGroups:
      title: Postman Groups Information
      type: object
      properties:
        data:
          type: array
          description: A list of Postman groups.
          items:
            type: object
            description: Information about the group.
            properties:
              id:
                type: number
                description: The group's ID.
                example: 123
              teamId:
                type: number
                description: The group's Postman team ID.
                example: 321
              name:
                type: string
                description: The group's name.
                example: API Test Group
              summary:
                type: string
                description: The group's summary.
                example: API testing group.
              createdBy:
                type: number
                description: The ID of ther user who created the group.
                example: ********
              createdAt:
                type: string
                format: date-time
                description: The date and time at which the group was created.
                example: '2024-10-03T14:05:05.000Z'
              updatedAt:
                type: string
                format: date-time
                description: The date and time at which the group was updated.
                example: '2024-10-03T14:05:05.000Z'
              members:
                type: array
                description: The group members' IDs.
                items:
                  type: number
                  example: ********
              roles:
                type: array
                description: The group's roles.
                items:
                  type: string
                  example: user
    jsonSchema:
      title: JSON Schema
      type: object
      required:
        - type
        - input
      properties:
        type:
          type: string
          description: The `json` type value.
          enum:
            - json
          example: json
        input:
          type: object
          description: An object that contains a valid JSON OpenAPI definition. For more information, read the [OpenAPI documentation](https://swagger.io/docs/specification/basic-structure/).
        options:
          type: object
          additionalProperties: true
          description: An object that contains advanced creation options and their values. You can find a complete list of properties and their values in Postman's [OpenAPI to Postman Collection Converter OPTIONS documentation](https://github.com/postmanlabs/openapi-to-postman/blob/develop/OPTIONS.md). These properties are case-sensitive.
    jsonStringified:
      title: JSON Stringified
      type: object
      required:
        - type
        - input
      properties:
        type:
          type: string
          description: The `string` type value.
          enum:
            - string
          example: string
        input:
          type: string
          description: The stringified OpenAPI definition.
          example: |-
            {
              "openapi": "3.0.0",
              "info": {
                "version": "1.0.0",
                "title": "Test API"
              },
              "servers": [
                {
                  "url": "http://locahost:3000"
                }
              ],
              "paths": {
                "/user": {
                  "get": {
                    "summary": "List all users",
                    "operationId": "listUser",
                    "parameters": [
                      {
                        "name": "id",
                        "in": "query",
                        "required": true,
                        "description": "The user's ID.",
                        "example": 1234,
                        "schema": {
                          "type": "integer",
                          "format": "int32"
                        }
                      }
                    ],
                    "responses": {
                      "200": {
                        "description": "Information about the user.",
                        "headers": {
                          "x-next": {
                            "description": "A link to the next page of responses.",
                            "schema": {
                              "type": "string"
                            }
                          }
                        },
                        "content": {
                          "application/json": {
                            "schema": {
                              "$ref": "#/components/schemas/User"
                            }
                          }
                        }
                      }
                    }
                  }
                }
              },
              "components": {
                "schemas": {
                  "User": {
                    "type": "object",
                    "required": [
                      "id",
                      "name"
                    ],
                    "properties": {
                      "id": {
                        "type": "integer",
                        "format": "int64"
                      },
                      "name": {
                        "type": "string"
                      },
                      "tag": {
                        "type": "string"
                      }
                    }
                  },
                  "Error": {
                    "type": "object",
                    "required": [
                      "code",
                      "message"
                    ],
                    "properties": {
                      "code": {
                        "type": "integer",
                        "format": "int32"
                      },
                      "message": {
                        "type": "string"
                      }
                    }
                  }
                }
              }
            }
        options:
          type: object
          additionalProperties: true
          description: An object that contains advanced creation options and their values. You can find a complete list of properties and their values in Postman's [OpenAPI to Postman Collection Converter OPTIONS documentation](https://github.com/postmanlabs/openapi-to-postman/blob/develop/OPTIONS.md). These properties are case-sensitive.
    getAuthenticatedUser:
      title: Successful Response
      type: object
      properties:
        user:
          type: object
          description: Information about the authenticated user.
          properties:
            id:
              type: number
              description: The user's Postman ID.
              example: ********
            username:
              type: string
              description: The user's username.
              example: taylor-lee
            email:
              type: string
              format: email
              description: The user's email address.
              example: <EMAIL>
            fullName:
              type: string
              description: The user's full name.
              example: Taylor Lee
            avatar:
              type: string
              format: url
              nullable: true
              description: The user's avatar image URL.
              example: https://example.com/user/r5u9qpvmujfjf6lbqmga.jpg
            isPublic:
              type: boolean
              description: If true, the user's information is public and visible to all users.
              example: true
            teamId:
              type: integer
              description: The team ID the user is assigned to. This returns a `0` value if the user is not assigned to a team.
              example: 123
            teamName:
              type: string
              description: The name of the team the user is assigned to.
              example: Test Team
            teamDomain:
              type: string
              description: The team's Postman domain ID.
              example: test-team
            roles:
              type: array
              description: A list of the user's assigned [roles](https://learning.postman.com/docs/collaborating-in-postman/roles-and-permissions/#team-roles).
              items:
                type: string
                default: user
                example: user
        operations:
          type: array
          description: Information about operations and their usage limits. This object does not return for users with the [Guest and Partner role](https://learning.postman.com/docs/collaborating-in-postman/roles-and-permissions/#team-roles).
          items:
            type: object
            properties:
              limit:
                type: number
                description: The operation's limit value.
                example: 1000000
              name:
                type: string
                description: The operation's name.
                example: mock_usage
              overage:
                type: number
                description: The operation's overage value.
                example: 0
              usage:
                type: number
                description: The operation's current usage value.
                example: 110276
    getMocks:
      title: Get Mock Servers
      type: object
      properties:
        mocks:
          type: array
          items:
            type: object
            description: Information about the mock servers.
            properties:
              id:
                type: string
                description: The mock server's ID.
                example: e3d951bf-873f-49ac-a658-b2dcb91d3289
              owner:
                type: string
                description: The ID of mock server's owner.
                example: '********'
              uid:
                type: string
                format: uid
                description: The mock server's unique ID.
                example: ********-e782b64e-406b-4a6c-8fe9-9ebe84aeb706
              collection:
                type: string
                description: The unique ID of the mock's associated collection.
                example: ********-39fee52f-b806-3ffa-1173-00a6f5b183dc
              mockUrl:
                type: string
                format: url
                description: The mock server URL.
                example: https://e3d951bf-873f-49ac-a658-b2dcb91d3289.mock.pstmn.io
              config:
                type: object
                description: Information about the mock server's configuration.
                properties:
                  delay:
                    type: object
                    nullable: true
                    description: Information about the mock server's simulated network delay settings. This returns a null value if there are no configured network delay settings.
                    properties:
                      type:
                        type: string
                        description: |
                          The type of simulated delay value:
                          - `fixed` — The delay value is a fixed value.
                        example: fixed
                        enum:
                          - fixed
                      preset:
                        type: string
                        description: |
                          The simulated fixed network delay value:
                          - `1` — 2G (300 ms).
                          - `2` — 3G (100 ms).
                          The object does not return this value for custom delay values.
                        example: '2'
                        enum:
                          - '1'
                          - '2'
                      duration:
                        type: integer
                        description: The configured delay, in milliseconds.
                        example: 100
                  headers:
                    type: array
                    description: A list of the mock server's headers.
                    items:
                      type: string
                    example: []
                  matchBody:
                    type: boolean
                    description: If true, match the request body.
                    example: false
                  matchQueryParams:
                    type: boolean
                    description: If true, match query parameters.
                    example: true
                  matchWildcards:
                    type: boolean
                    description: If true, use wildcard variable matching.
                    example: true
                  serverResponseId:
                    type: string
                    description: The ID of mock server's default response for requests. All calls to the mock server will return the defined response.
                    example: 94d0f5d9-dbdf-4046-9a7d-a11f9b78ac65
              createdAt:
                type: string
                format: date-time
                description: The date and time at which the mock server was created.
                example: '2022-06-09T19:00:39.000Z'
              environment:
                type: string
                description: The mock server's associated environment ID.
                example: 1679925-0b9e9f15-3208-a2b1-22e0-d58392f01524
              isPublic:
                type: boolean
                description: If true, the mock server is public and visible to all users. This field does not indicate the mock server's access control status.
                example: false
              name:
                type: string
                description: The mock server's name.
                example: Test Mock
              updatedAt:
                type: string
                format: date-time
                description: The date and time at which the mock server was last updated.
                example: '2022-06-09T19:00:39.000Z'
    createMock:
      title: Create Mock
      type: object
      properties:
        mock:
          type: object
          required:
            - collection
          properties:
            collection:
              type: string
              format: uid
              description: The unique ID of the mock's associated collection.
              example: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
            environment:
              type: string
              format: uid
              description: The unique ID of the mock's associated environment.
              example: ********-5daabc50-8451-43f6-922d-96b403b4f28e
            name:
              type: string
              description: The mock server's name.
              example: Test Mock
            private:
              type: boolean
              default: false
              description: If true, the mock server is set private. By default, mock servers are public and can receive requests from anyone and anywhere.
              example: true
    mockCreateUpdateResponse:
      title: Mock Create/Update Response
      type: object
      properties:
        mock:
          type: object
          description: Information about the mock server.
          properties:
            id:
              type: string
              description: The mock server's ID.
              example: e3d951bf-873f-49ac-a658-b2dcb91d3289
            owner:
              type: string
              description: The ID of mock server's owner.
              example: '********'
            uid:
              type: string
              format: uid
              description: The mock server's unique ID.
              example: ********-e3d951bf-873f-49ac-a658-b2dcb91d3289
            collection:
              type: string
              description: The unique ID of the mock's associated collection.
              example: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
            mockUrl:
              type: string
              format: url
              description: The mock server URL.
              example: https://e3d951bf-873f-49ac-a658-b2dcb91d3289.mock.pstmn.io
            config:
              type: object
              description: Information about the mock server's configuration.
              additionalProperties: true
              properties:
                matchBody:
                  type: boolean
                  description: If true, the mock server matches the request's body to the body of the saved examples.
                  example: false
                matchHeader:
                  type: boolean
                  description: If true, the mock server matches the request's headers in the `header` array to the headers of the saved examples.
                  example: true
                headers:
                  type: array
                  description: A list of header key values.
                  items:
                    type: string
                    example: POST
                matchQueryParams:
                  type: boolean
                  description: If true, match query parameters to the saved examples.
                  example: false
                matchWildcards:
                  type: boolean
                  description: If true, use wildcard variable matching.
                delay:
                  type: object
                  nullable: true
                  description: Information about the mock server's simulated network delay settings. This returns a null value if there are no configured network delay settings.
                  properties:
                    type:
                      type: string
                      description: The `fixed` value.
                      enum:
                        - fixed
                      example: fixed
                    duration:
                      type: integer
                      description: For custom network delays, the custom configured delay, in milliseconds.
                      example: 700
                    preset:
                      type: string
                      description: |
                        The simulated fixed network delay value:
                        - `1` — 2G (300 ms).
                        - `2` — 3G (100 ms).
                        The object does not return this value for custom delay values.
                      enum:
                        - '1'
                        - '2'
                      example: '2'
                serverResponseId:
                  type: string
                  nullable: true
                  description: The ID of mock server's default response for requests. All calls to the mock server will return the defined response.
                  example: 9a291bbe-dc0a-44ba-a3c8-6dbd06a61460
            createdAt:
              type: string
              format: date-time
              description: The date and time at which the mock server was created.
              example: '2022-06-09T19:00:39.000Z'
            updatedAt:
              type: string
              format: date-time
              description: The date and time at which the mock server was last updated.
              example: '2022-06-09T19:00:39.000Z'
            environment:
              type: string
              description: The unique ID of the mock's associated environment.
              example: ********-5daabc50-8451-43f6-922d-96b403b4f28e
    getMock:
      title: Get Mock Server
      type: object
      properties:
        mock:
          type: object
          properties:
            id:
              type: string
              description: The mock server's ID.
              example: e3d951bf-873f-49ac-a658-b2dcb91d3289
            owner:
              type: string
              description: The ID of mock server's owner.
              example: '********'
            uid:
              type: string
              format: uid
              description: The mock server's unique ID.
              example: ********-e782b64e-406b-4a6c-8fe9-9ebe84aeb706
            collection:
              type: string
              description: The unique ID of the mock's associated collection.
              example: ********-39fee52f-b806-3ffa-1173-00a6f5b183dc
            mockUrl:
              type: string
              format: url
              description: The mock server URL.
              example: https://e3d951bf-873f-49ac-a658-b2dcb91d3289.mock.pstmn.io
            name:
              type: string
              description: The mock server's name.
              example: Test Mock
            config:
              type: object
              description: Information about the mock server's configuration.
              properties:
                headers:
                  type: array
                  description: A list of the mock server's headers.
                  items:
                    type: string
                matchBody:
                  type: boolean
                  description: If true, match the request body.
                  example: false
                matchQueryParams:
                  type: boolean
                  description: If true, match query parameters.
                  example: true
                matchWildcards:
                  type: boolean
                  description: If true, use wildcard variable matching.
                  example: true
                serverResponseId:
                  type: string
                  nullable: true
                  description: The ID of mock server's default response for requests. All calls to the mock server will return the defined response.
                  example: 94d0f5d9-dbdf-4046-9a7d-a11f9b78ac65
            createdAt:
              type: string
              format: date-time
              description: The date and time at which the mock server was created.
              example: '2022-06-09T19:00:39.000Z'
            updatedAt:
              type: string
              format: date-time
              description: The date and time at which the mock server was last updated.
              example: '2022-06-09T19:00:39.000Z'
            isPublic:
              type: boolean
              description: If true, the mock server is public and visible to all users. This field does not indicate the mock server's access control status.
              example: false
            deactivated:
              type: boolean
              description: If true, the mock server is not active. Mock servers deactivate when a linked collection or environment is deleted.
              example: false
            environment:
              type: string
              description: The mock server's associated environment ID.
              example: 1679925-0b9e9f15-3208-a2b1-22e0-d58392f01524
    mock400ErrorInstanceNotFound:
      title: Not Found
      type: object
      properties:
        error:
          type: object
          properties:
            name:
              type: string
              description: The error name.
              example: instanceNotFoundError
            message:
              type: string
              description: The error message.
              example: The specified mock does not exist.
    updateMock:
      title: Update Mock
      type: object
      properties:
        mock:
          type: object
          properties:
            name:
              type: string
              description: The mock server's name.
              example: Test Mock
            environment:
              type: string
              format: uid
              description: The associated environment's unique ID.
              example: ********-5daabc50-8451-43f6-922d-96b403b4f28e
            description:
              type: string
              description: The mock server's description.
              example: This is a test mock server.
            private:
              type: boolean
              default: false
              description: If true, the mock server is set private. By default, mock servers are public and can receive requests from anyone and anywhere.
              example: true
            versionTag:
              type: string
              description: The API's version tag ID.
              example: abf07d3d-f8ec-47d4-8015-9fe83078b4ec
            config:
              type: object
              description: The mock server's configuration settings.
              properties:
                serverResponseId:
                  type: string
                  nullable: true
                  description: The server response ID. This sets the given server response as the default response for each request. To deactivate a server response, pass a null value.
                  example: 9a291bbe-dc0a-44ba-a3c8-6dbd06a61460
    deleteMock:
      title: Mock Deleted
      type: object
      properties:
        mock:
          type: object
          description: Information about the mock server.
          properties:
            id:
              type: string
              description: The mock server's ID.
              example: e3d951bf-873f-49ac-a658-b2dcb91d3289
            uid:
              type: string
              format: uid
              description: The mock server's unique ID.
              example: ********-e782b64e-406b-4a6c-8fe9-9ebe84aeb706
    getMockCallLogs:
      title: Get Call Logs
      type: object
      properties:
        call-logs:
          type: array
          items:
            type: object
            description: Information about the mock server's server responses.
            properties:
              id:
                type: string
                description: The server response's ID.
                example: 0f63f54d-665e-436a-95b4-c1302d7685a9-3925
              responseName:
                type: string
                description: The server response's name.
                example: Bad Request
              servedAt:
                type: string
                format: date-time
                description: The date and time at which the server response was served.
                example: '2022-01-17T06:19:22.000Z'
              request:
                type: object
                description: The server response's request information.
                properties:
                  method:
                    type: string
                    description: The request method.
                    example: POST
                  path:
                    type: string
                    description: The request's path.
                    example: /animals
                  headers:
                    type: object
                    description: The request's headers.
                    properties:
                      key:
                        type: string
                        description: The request header's name.
                        example: content-type
                      value:
                        type: string
                        description: The request header's value.
                        example: application/json
                  body:
                    type: object
                    description: The request's body information.
                    properties:
                      mode:
                        type: string
                        description: The request body's media type (mode).
                        example: raw
                      data:
                        type: string
                        description: The request body's contents.
                        example: '{"type":"hamster","breed":"Syrian Hamster","age":"1 month"}'
              response:
                type: object
                description: The server response's response information.
                properties:
                  type:
                    type: string
                    description: The type of response.
                  statusCode:
                    type: number
                    description: The response's status code.
                    example: 400
                  headers:
                    type: object
                    description: The response's headers.
                    properties:
                      description:
                        type: object
                        description: The response header's description information.
                        properties:
                          content:
                            type: string
                            description: The response header description's content.
                            example: ''
                          type:
                            type: string
                            description: The response header description's media type.
                            example: text/plain
                      key:
                        type: string
                        description: The response header's name.
                        example: content-type
                      value:
                        type: string
                        description: The response header's value.
                        example: application/json
                  body:
                    type: object
                    description: The response's body information.
                    properties:
                      data:
                        type: string
                        description: The response body's contents.
                        example: "{\n\t\"message\": \"Malformed request. You need to send gender as part of the request.\"\n}"
        meta:
          type: object
          description: The response's non-standard meta information.
          properties:
            nextCursor:
              type: string
              nullable: true
              format: base64
              description: The pagination cursor that points to the next record in the results set.
              example: eyJzY2hlbWUiOiJwYXRoX2FzYyIsImRpcmVjdGlvblR5cGUiOiJuZXh0IiwicGl2b3QiOiJwYXRoIiwidmFsdWUiOiJOZXdQZXQuanNvbiJ9
    mock400ErrorLogRetentionPeriodExceeded:
      title: Log Retention Period Exceeded
      type: object
      properties:
        error:
          type: object
          properties:
            name:
              type: string
              description: The error name.
              example: invalidParamsError
            message:
              type: string
              description: The error message.
              example: 'Invalid value for parameter: since. Cannot view logs beyond the call log retention period based on your plan i.e. 30 days.'
    publishUnpublishMockResponse:
      title: Mock Published/Unpublished
      type: object
      properties:
        mock:
          type: object
          properties:
            id:
              type: string
              description: The mock server's ID.
              example: e3d951bf-873f-49ac-a658-b2dcb91d3289
    mock400ErrorAlreadyPublished:
      title: Bad Request
      type: object
      properties:
        error:
          type: object
          properties:
            name:
              type: string
              description: The error name.
              example: mockAlreadyPublishedError
            message:
              type: string
              description: The error message.
              example: This mock is already public.
    mock400ErrorAlreadyUnpublished:
      title: Bad Request
      type: object
      properties:
        error:
          type: object
          properties:
            name:
              type: string
              description: The error name.
              example: mockAlreadyUnpublishedError
            message:
              type: string
              description: The error message.
              example: This mock has already been deleted.
    getMockServerResponses:
      title: Successful Response
      type: array
      description: Information about the mock server's responses.
      items:
        type: object
        description: Information about the server response.
        properties:
          id:
            type: string
            description: The server response's ID.
            example: 965cdd16-fe22-4d96-a161-3d05490ac421
          name:
            type: string
            description: The server response's name.
            example: Internal Server Error
          statusCode:
            type: number
            description: The server response's 5xx HTTP response code.
            example: 500
          createdAt:
            type: string
            format: date-time
            description: The date and time at which the server response was created.
            example: '2022-08-02T14:57:44.000Z'
          updatedAt:
            type: string
            format: date-time
            description: The date and time at which the server response was last updated.
            example: '2022-08-02T14:57:44.000Z'
          createdBy:
            type: string
            description: The user ID of the user who created the server response.
            example: '********'
          updatedBy:
            type: string
            description: The user ID of the user who last updated the server response.
            example: '********'
    createMockServerResponse:
      title: Create Mock Server Response
      type: object
      properties:
        serverResponse:
          type: object
          required:
            - name
            - statusCode
          properties:
            name:
              type: string
              description: The server response's name.
              example: Internal Server Error
            statusCode:
              type: integer
              description: The server response's 5xx HTTP response code. This property only accepts 5xx values.
              example: 500
            headers:
              type: array
              description: The server response's request headers, such as Content-Type, Accept, encoding, and other information.
              items:
                type: object
                properties:
                  key:
                    type: string
                    description: The request header's key value.
                    example: Content-Type
                  value:
                    type: string
                    description: The request header's value.
                    example: application/json
            language:
              type: string
              nullable: true
              description: The server response's body language type.
              enum:
                - text
                - javascript
                - json
                - html
                - xml
              example: json
            body:
              type: string
              description: The server response's body that returns when calling the mock server.
              example: |-
                {
                    "message": "Something went wrong; try again later."
                }
    mockServerResponse:
      title: Successful Response
      type: object
      description: Information about the mock server's responses.
      additionalProperties: true
      properties:
        createdAt:
          type: string
          format: date-time
          description: The date and time at which the server response was created.
          example: '2022-08-02T14:57:44.000Z'
        updatedAt:
          type: string
          format: date-time
          description: The date and time at which the server response was last updated.
          example: '2022-08-02T14:57:44.000Z'
        id:
          type: string
          description: The server response's ID.
          example: 965cdd16-fe22-4d96-a161-3d05490ac421
        name:
          type: string
          description: The server response's name.
          example: Internal Server Error
        statusCode:
          type: number
          description: The server response's 5xx HTTP response code.
          example: 500
        headers:
          type: array
          description: The server response's request headers key-value pairs, such as Content-Type, Accept, encoding, and other information.
          items:
            type: object
            description: Information about the key-value pair.
            properties:
              key:
                type: string
                description: The request header's key value.
                example: Content-Type
              value:
                type: string
                description: The request header's value.
                example: application/json
        language:
          type: string
          description: The server response's body language type.
          example: json
        body:
          type: string
          description: The server response's body that returns when calling the mock server.
          example: |-
            {
                "message": "Something went wrong; try again later."
            }
        createdBy:
          type: string
          description: The user ID of the user who created the server response.
          example: '********'
        updatedBy:
          type: string
          description: The user ID of the user who last updated the server response.
          example: '********'
        mock:
          type: string
          description: The associated mock server's ID.
          example: 32cd624d-9986-4f20-9048-89252f722269
    serverResponseNotFound400Error:
      title: Bad Request
      type: object
      properties:
        error:
          type: object
          properties:
            name:
              type: string
              description: The error name.
              example: serverResponseNotFoundError
            message:
              type: string
              description: The error message.
              example: We could not find the mock server response you are looking for.
    updateMockServerResponse:
      title: Update Mock Server Response
      type: object
      properties:
        serverResponse:
          type: object
          properties:
            name:
              type: string
              description: The server response's name.
              example: Internal Server Error
            statusCode:
              type: integer
              description: The server response's 5xx HTTP response code. This property only accepts [5xx values](https://www.iana.org/assignments/http-status-codes/http-status-codes.xhtml).
              example: 500
            headers:
              type: array
              description: The server response's request headers, such as Content-Type, Accept, encoding, and other information.
              items:
                type: object
                description: Information about the key-value pair.
                properties:
                  key:
                    type: string
                    description: The request header's key value.
                    example: Content-Type
                  value:
                    type: string
                    description: The request header's value. This value defines the corresponding value for the header key.
                    example: application/json
            language:
              type: string
              nullable: true
              description: The server response's body language type.
              enum:
                - text
                - javascript
                - json
                - html
                - xml
              example: json
            body:
              type: string
              description: The server response's body that returns when you call the mock server.
              example: |-
                {
                    "message": "Something went wrong; try again later."
                }
    deleteMockServerResponse:
      title: Server Response Deleted
      type: object
      description: Information about the deleted server response.
      properties:
        id:
          type: string
          description: The server response's ID.
          example: 965cdd16-fe22-4d96-a161-3d05490ac421
        name:
          type: string
          description: The server response's name.
          example: Internal Server Error
        statusCode:
          type: number
          description: The server response's 5xx HTTP response code.
          example: 500
        headers:
          type: array
          description: The server response's request headers, such as Content-Type, Accept, encoding, and other information.
          items:
            type: object
            properties:
              key:
                type: string
                description: The request header's key value.
                example: Content-Type
              value:
                type: string
                description: The request header's value.
                example: application/json
        language:
          type: string
          nullable: true
          description: The server response's body language type.
          enum:
            - text
            - javascript
            - json
            - html
            - xml
          example: json
        body:
          type: string
          description: The server response's body that returns when calling the mock server.
          example: |-
            {
                "message": "Something went wrong; try again later."
            }
        createdBy:
          type: string
          description: The user ID of the user who created the server response.
          example: '********'
        updatedBy:
          type: string
          description: The user ID of the user who last updated the server response.
          example: '********'
        createdAt:
          type: string
          format: date-time
          description: The date and time at which the server response was created.
          example: '2022-09-21T20:20:09.000Z'
    getMonitors:
      title: Successful Response
      type: object
      properties:
        monitors:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
                description: The monitor's ID.
                example: 1e6b6cc1-c760-48e0-968f-4bfaeeae9af1
              name:
                type: string
                description: The monitor's name.
                example: Test Monitor
              owner:
                type: string
                description: The ID of the monitor's owner.
                example: '********'
              uid:
                type: string
                format: uid
                description: The monitor's unique ID.
                example: ********-1e6b6cc1-c760-48e0-968f-4bfaeeae9af1
    createMonitor:
      title: Create Monitor
      type: object
      properties:
        monitor:
          type: object
          properties:
            collection:
              type: string
              description: The unique ID of the monitor's associated collection.
              example: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
            environment:
              type: string
              description: The unique ID of the monitor's associated environment.
              example: ********-5daabc50-8451-43f6-922d-96b403b4f28e
            name:
              type: string
              description: The monitor's name.
              example: Test Monitor
            schedule:
              type: object
              description: Information about the monitor's schedule.
              properties:
                cron:
                  type: string
                  description: |
                    The monitor's run frequency, based on the given cron pattern. For example:

                    | Frequency | Cron pattern |
                    | --------- | ------------ |
                    | Every 5 minutes | `*/5 * * * *` |
                    | Every 30 minutes | `*/30 * * * *` |
                    | Every hour | `0 */1 * * *` |
                    | Every 6 hours | `0 */6 * * *` |
                    | Every day at 5 pm | `0 17 * * *` |
                    | Every Monday at 12 pm | `0 12 * * MON` |
                    | Every weekday (Mon — Fri) at 6 am | `0 6 * * MON-FRI` |
                  example: '*/5 * * * *'
                timezone:
                  type: string
                  description: The monitor's [timezone](https://en.wikipedia.org/wiki/List_of_tz_database_time_zones).
                  example: America/Chicago
    createMonitorResponse:
      title: Monitor Created
      type: object
      properties:
        monitor:
          type: object
          properties:
            id:
              type: string
              description: The monitor's ID.
              example: 1e6b6cc1-c760-48e0-968f-4bfaeeae9af1
            name:
              type: string
              description: The monitor's name.
              example: Test Monitor
            uid:
              type: string
              format: uid
              description: The monitor's unique ID.
              example: ********-1e6b6cc1-c760-48e0-968f-4bfaeeae9af1
    monitors400ErrorInvalidCronPattern:
      title: Invalid Cron Pattern
      type: object
      properties:
        error:
          type: object
          properties:
            name:
              type: string
              description: The error name.
              example: cronPatternNotAllowedError
            message:
              type: string
              description: The error message.
              example: Invalid cron pattern. Enter a valid cron pattern, such as "0 17 * * *"
            details:
              type: object
              description: Information about the error.
              properties:
                pattern:
                  type: string
                  description: The invalid cron pattern.
                  example: '* * * * *'
    monitors400ErrorInvalidTimezone:
      title: Invalid Timezone
      type: object
      properties:
        error:
          type: object
          properties:
            name:
              type: string
              description: The error name.
              example: invalidParamsError
            message:
              type: string
              description: The error message.
              example: The request had invalid parameters
            details:
              type: object
              description: Information about the error.
              properties:
                param:
                  type: string
                  description: The invalid parameter.
                  example: schedule.timezone
    monitors400ErrorInvalidUid:
      title: Invalid UID
      type: object
      properties:
        error:
          type: object
          properties:
            name:
              type: string
              description: The error name.
              example: invalidUidError
            message:
              type: string
              description: The error message.
              example: The specified uid is invalid.
            details:
              type: object
              description: Information about the error.
              properties:
                param:
                  type: string
                  description: The invalid parameter.
                  example: collection
                uid:
                  type: string
                  format: uid
                  description: The invalid UID.
                  example: 5daabc50-8451-43f6-922d-96b403b4f28e
    monitors400ParamMissing:
      title: Missing Parameter
      type: object
      properties:
        error:
          type: object
          properties:
            name:
              type: string
              description: The error name.
              example: paramMissingError
            message:
              type: string
              description: The error message.
              example: Parameter is missing in the request.
            details:
              type: object
              description: Information about the error.
              properties:
                param:
                  type: array
                  description: A list of the missing parameters.
                  items:
                    type: string
                    example: name
    getMonitor:
      title: Successful Response
      type: object
      properties:
        monitor:
          type: object
          properties:
            id:
              type: string
              description: The monitor's ID.
              example: 1e6b6cc1-c760-48e0-968f-4bfaeeae9af1
            name:
              type: string
              description: The monitor's name.
              example: Test Monitor
            uid:
              type: string
              format: uid
              description: The monitor's unique ID.
              example: ********-1e6b6cc1-c760-48e0-968f-4bfaeeae9af1
            owner:
              type: number
              description: The ID of monitor's owner.
              example: ********
            collectionUid:
              type: string
              format: uid
              description: The unique ID of the monitor's associated collection.
              example: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
            environmentUid:
              type: string
              format: uid
              description: The unique ID of the monitor's associated environment.
              example: ********-5daabc50-8451-43f6-922d-96b403b4f28e
            distribution:
              type: array
              description: A list of the monitor's [geographic regions](https://learning.postman.com/docs/monitoring-your-api/setting-up-monitor/#adding-regions).
              items:
                type: string
              example: []
            lastRun:
              type: object
              description: Information about the monitor's previous run.
              properties:
                finishedAt:
                  type: string
                  format: date-time
                  description: The date and time at which the monitor's previous run completed.
                  example: '2022-06-17T18:39:53.707Z'
                startedAt:
                  type: string
                  format: date-time
                  description: The date and time at which the monitor's previous run started.
                  example: '2022-06-17T18:39:52.852Z'
                stats:
                  type: object
                  description: Information about the monitor's stats.
                  properties:
                    assertions:
                      type: object
                      description: Information about the monitor's assertions.
                      properties:
                        failed:
                          type: number
                          description: The total number of test failures.
                          example: 1
                        total:
                          type: number
                          description: The total number of tests performed.
                          example: 8
                    requests:
                      type: object
                      description: Information about the monitor's requests.
                      properties:
                        total:
                          type: number
                          description: The total number of requests.
                          example: 4
                status:
                  type: string
                  description: The monitor's status after its last run.
                  example: failed
            notifications:
              type: object
              description: Information about the monitor's notification settings.
              properties:
                onError:
                  type: array
                  items:
                    type: object
                    properties:
                      email:
                        type: string
                        format: email
                        description: The email address of the user to notify on monitor error.
                        example: <EMAIL>
                onFailure:
                  type: array
                  items:
                    type: object
                    properties:
                      email:
                        type: string
                        format: email
                        description: The email address of the user to notify on monitor failure.
                        example: <EMAIL>
            options:
              type: object
              description: Information about the monitor's option settings.
              properties:
                followRedirects:
                  type: boolean
                  description: If true, follow redirects enabled.
                  example: true
                requestDelay:
                  type: number
                  description: The monitor's request delay value.
                  example: 0
                requestTimeout:
                  type: number
                  description: The monitor's request timeout value.
                  example: 3000
                strictSSL:
                  type: boolean
                  description: If true, strict SSL enabled.
                  example: true
            schedule:
              type: object
              description: Information about the monitor's schedule.
              properties:
                cron:
                  type: string
                  description: The monitor's cron frequency value.
                  example: 0 0 * * * *
                nextRun:
                  type: string
                  format: date-time
                  description: The date and time of monitor's next scheduled run.
                  example: '2022-06-18T05:00:00.000Z'
                timezone:
                  type: string
                  description: The monitor's timezone.
                  example: America/Chicago
    instanceNotFoundMonitor:
      title: Instance Not Found
      type: object
      properties:
        error:
          type: object
          properties:
            name:
              type: string
              description: The error name.
              example: instanceNotFoundError
            message:
              type: string
              description: The error message.
              example: The specified monitor does not exist.
    updateMonitor:
      title: Update Monitor
      type: object
      properties:
        monitor:
          type: object
          properties:
            name:
              type: string
              description: The monitor's name.
              example: Test Monitor
            schedule:
              type: object
              description: Information about the monitor's schedule.
              properties:
                cron:
                  type: string
                  description: |
                    The monitor's run frequency, based on the given cron pattern. For example:

                    | Frequency | Cron pattern |
                    | --------- | ------------ |
                    | Every 5 minutes | `*/5 * * * *` |
                    | Every 30 minutes | `*/30 * * * *` |
                    | Every hour | `0 */1 * * *` |
                    | Every 6 hours | `0 */6 * * *` |
                    | Every day at 5 pm | `0 17 * * *` |
                    | Every Monday at 12 pm | `0 12 * * MON` |
                    | Every weekday (Mon — Fri) at 6 am | `0 6 * * MON-FRI` |
                  example: '*/5 * * * *'
                timezone:
                  type: string
                  description: The monitor's [timezone](https://en.wikipedia.org/wiki/List_of_tz_database_time_zones).
                  example: America/Chicago
    updateMonitorResponse:
      title: Monitor Updated
      type: object
      properties:
        monitor:
          type: object
          properties:
            id:
              type: string
              description: The monitor's ID.
              example: 1e6b6cc1-c760-48e0-968f-4bfaeeae9af1
            name:
              type: string
              description: The monitor's name.
              example: Test Monitor
            uid:
              type: string
              format: uid
              description: The monitor's unique ID.
              example: ********-1e6b6cc1-c760-48e0-968f-4bfaeeae9af1
    deleteMonitor:
      title: Monitor Deleted
      type: object
      properties:
        monitor:
          type: object
          properties:
            id:
              type: string
              description: The monitor's ID.
              example: 1e6b6cc1-c760-48e0-968f-4bfaeeae9af1
            uid:
              type: string
              format: uid
              description: The monitor's unique ID.
              example: ********-1e6b6cc1-c760-48e0-968f-4bfaeeae9af1
    runMonitor:
      title: Successful Response
      type: object
      properties:
        run:
          type: object
          description: Information about the monitor run. If you pass the `async=true` query parameter, the response does not return the `stats`, `executions`, and `failures` responses. To get this information for an asynchronous run, call the GET `/monitors/{id}` endpoint.
          properties:
            info:
              type: object
              description: Information about the monitor.
              properties:
                jobId:
                  type: string
                  description: The monitor's run job ID.
                  example: 1ecee76a-e14e-47c0-bddc-256bf690c407
                collectionUid:
                  type: string
                  format: uid
                  description: The unique ID of the monitor's associated collection.
                  example: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
                environmentUid:
                  type: string
                  format: uid
                  description: The unique ID of the monitor's associated environment.
                  example: ********-5daabc50-8451-43f6-922d-96b403b4f28e
                monitorId:
                  type: string
                  description: The monitor's ID.
                  example: 1e6b6cc1-c760-48e0-968f-4bfaeeae9af1
                name:
                  type: string
                  description: The monitor's name.
                  example: Test Monitor
                status:
                  type: string
                  description: The monitor run's status.
                  example: success
                startedAt:
                  type: string
                  format: date-time
                  description: The date and time at which the monitor run began.
                  example: '2022-06-17T19:50:04.019Z'
                finishedAt:
                  type: string
                  format: date-time
                  description: The date and time at which the monitor's run completed.
                  example: '2022-06-17T19:50:06.439Z'
            stats:
              type: object
              description: Information about the monitor run's stats.
              properties:
                assertions:
                  type: object
                  description: The monitor run's assertions stats.
                  properties:
                    total:
                      type: number
                      description: The total number of tests performed.
                      example: 0
                    failed:
                      type: number
                      description: The total number of test failures.
                      example: 0
                requests:
                  type: object
                  description: The monitor run's request stats.
                  properties:
                    total:
                      type: number
                      description: The total number of requests.
                      example: 1
                    failed:
                      type: number
                      description: The number of request failures.
                      example: 0
            executions:
              type: array
              description: Information about the monitor run's executions.
              items:
                type: object
                properties:
                  id:
                    type: number
                    description: The execution ID.
                    example: 1
                  item:
                    type: object
                    description: Information about the executed item.
                    properties:
                      name:
                        type: string
                        description: The executed item's name.
                        example: Sample POST Request
                  request:
                    type: object
                    description: Information about the monitor run's requests.
                    properties:
                      method:
                        type: string
                        description: The request method.
                        example: POST
                      url:
                        type: string
                        format: url
                        description: The request's URL.
                        example: http://echo.getpostman.com/post
                      body:
                        type: object
                        description: Information about the request body, such as Content-Length.
                        additionalProperties: true
                      headers:
                        type: object
                        description: Information about the request headers, such as Content-Type, Accept, encoding, and other information.
                        additionalProperties: true
                      timestamp:
                        type: string
                        description: The date and time of the request.
                        example: '2016-12-04T14:30:26.025Z'
                  response:
                    type: object
                    description: Information about the monitor run's response.
                    properties:
                      body:
                        type: object
                        description: Information about the request body, such as Content-Length.
                        additionalProperties: true
                      code:
                        type: number
                        description: The response's HTTP status code.
                        example: 200
                      headers:
                        type: object
                        description: Information about the response headers, such as Content-Type, Accept, encoding, and other information.
                        additionalProperties: true
                      responseSize:
                        type: number
                        description: The response size, in bytes.
                        example: 298
                      responseTime:
                        type: number
                        description: The response time, in milliseconds.
                        example: 26
            failures:
              type: array
              description: If the monitor run failed, information about the run's failures.
              items:
                type: object
              example: []
    getPanElementsAndFolders:
      title: Successful Response
      type: object
      properties:
        elements:
          description: Information about your team's Private API Network folder elements. Elements are APIs, collections, and workspaces.
          type: array
          items:
            type: object
            properties:
              createdAt:
                type: string
                format: date-time
                description: The date and time at which the element was created.
                example: '2020-06-01T08:32:31.000Z'
              createdBy:
                type: integer
                description: The user who created the element.
                example: ********
              updatedAt:
                type: string
                format: date-time
                description: The date and time at which the element was last updated.
                example: '2021-11-29T06:31:24.000Z'
              updatedBy:
                type: integer
                description: The user who updated the element.
                example: ********
              addedAt:
                type: string
                format: date-time
                description: The date and time at which the element was published to your team's Private API Network. This value is the same as the `updatedAt` value.
                example: '2021-11-29T06:31:24.000Z'
              addedBy:
                type: integer
                description: The user ID of the user who published the element.
                example: ********
              description:
                type: string
                description: The element's description.
                example: The payments and account services API.
              id:
                type: string
                description: The element's ID.
                example: 5360b75f-447e-467c-9299-12fd6c92450d
              name:
                type: string
                description: The element's name.
                example: Billing API
              summary:
                type: string
                description: The element's summary.
                example: Payments and Account Services API
              type:
                type: string
                description: The element's type.
                example: api
              parentFolderId:
                type: integer
                description: The element's parent folder ID.
                example: 1
              href:
                type: string
                format: url
                description: The element's HREF.
                example: https://api.getpostman.com/apis/5360b75f-447e-467c-9299-12fd6c92450d
        folders:
          type: array
          description: Information about your team's Private API Network folders.
          items:
            type: object
            properties:
              id:
                type: integer
                description: The folder's ID.
                example: 1
              parentFolderId:
                type: integer
                description: The folder's parent folder ID.
                example: 0
              updatedAt:
                type: string
                format: date-time
                description: The date and time at which the folder was updated.
                example: '2021-11-29T06:31:24.000Z'
              updatedBy:
                type: integer
                description: The user ID of the user who updated the folder.
                example: ********
              createdAt:
                type: string
                format: date-time
                description: The date and time at which the folder was created.
                example: '2020-06-01T08:32:31.000Z'
              createdBy:
                type: integer
                description: The user who created the folder.
                example: ********
              name:
                type: string
                description: The folder's name.
                example: Billing
              description:
                type: string
                description: The folder's description.
                example: The Billing API.
              type:
                type: string
                description: The element's type. This value is always `folder`.
                example: folder
        meta:
          type: object
          description: The response's non-standard meta information.
          properties:
            limit:
              type: integer
              description: The maximum number of elements returned. If the value exceeds the maximum value of `1000`, then the system uses the `1000` value.
              example: 1000
            offset:
              type: integer
              example: 0
              description: The zero-based offset of the first item returned.
            totalCount:
              type: integer
              example: 2
              description: The total count of the `elements` and `folders` items.
    panCreateApi:
      title: Add API
      type: object
      properties:
        api:
          type: object
          required:
            - id
            - parentFolderId
          properties:
            id:
              type: string
              description: The API's ID.
              example: 5360b75f-447e-467c-9299-12fd6c92450d
            parentFolderId:
              type: integer
              description: The API's parent folder ID.
              example: 1
    panCreateCollection:
      title: Add Collection
      type: object
      properties:
        collection:
          type: object
          required:
            - id
            - parentFolderId
          properties:
            id:
              type: string
              description: The collection's ID.
              example: 5360b75f-447e-467c-9299-12fd6c92450d
            parentFolderId:
              type: integer
              description: The collection's parent folder ID.
              example: 1
            environments:
              type: array
              description: A list of environment UIDs (`userId`-`environmentId``) to add to the collection.
              items:
                type: string
                format: uid
                description: An environment's UID.
                example: ********-622c084d-4f48-470b-826e-18af3c65c775
    panCreateWorkspace:
      title: Add Workspace
      type: object
      properties:
        workspace:
          type: object
          required:
            - id
            - parentFolderId
          properties:
            id:
              type: string
              description: The workspace's ID.
              example: 5360b75f-447e-467c-9299-12fd6c92450d
            parentFolderId:
              type: integer
              description: The workspace's parent folder ID.
              example: 1
    panCreateFolder:
      title: Add Folder
      type: object
      properties:
        folder:
          type: object
          required:
            - name
          properties:
            name:
              type: string
              description: The folder's name.
              example: Billing
            description:
              type: string
              description: The folder's description.
              example: The Billing API.
            parentFolderId:
              type: integer
              default: 0
              description: The folder's parent folder ID. This value defaults to `0`. To create a folder at the root level, omit this property.
              example: 0
    panElementCreated:
      title: Element Created
      description: Information about the Private API Network element.
      type: object
      properties:
        addedAt:
          type: string
          format: date-time
          description: The date and time at which the element was added.
          example: '2022-06-09T14:48:45.000Z'
        addedBy:
          type: integer
          description: The user who added the element.
          example: ********
        createdBy:
          type: integer
          description: The user who created the element.
          example: ********
        createdAt:
          type: string
          format: date-time
          description: The date and time at which the element was created.
          example: '2021-10-11T09:39:33.000Z'
        updatedBy:
          type: integer
          description: The user who last updated the element.
          example: ********
        updatedAt:
          type: string
          format: date-time
          description: The date and time at which the element was last updated.
          example: '2022-12-07T17:54:33.000Z'
        type:
          type: string
          description: The element's type.
          example: api
        id:
          type: string
          description: The element's ID or UID.
          example: 5360b75f-447e-467c-9299-12fd6c92450d
        name:
          type: string
          description: The element's name.
          example: Billing API
        summary:
          type: string
          nullable: true
          description: The element's summary.
          example: The payments and account services API.
        description:
          type: string
          description: The element's description.
          example: ''
        href:
          type: string
          format: url
          description: The element's Postman URL.
          example: https://api.getpostman.com/collections/********-12ece9e1-2abf-4edc-8e34-de66e74114d2
        parentFolderId:
          type: integer
          description: The parent folder's ID.
          example: 1
        environments:
          type: array
          description: A list of the element's environments.
          items:
            type: string
            example: 120403-c6cf0fe2-c637-4d30-a640-ed64ec6d0ddb
    panFolderCreated:
      title: Folder Created
      type: object
      description: Information about the Private API Network folder.
      properties:
        id:
          type: integer
          description: The folder's ID.
          example: 1
        parentFolderId:
          type: integer
          description: The parent folder ID.
          example: 0
        updatedAt:
          type: string
          format: date-time
          description: The date and time at which the folder was updated.
          example: '2022-12-07T18:00:39.000Z'
        updatedBy:
          type: integer
          description: The user who updated the folder.
          example: ********
        createdBy:
          type: integer
          description: The user who created the folder.
          example: ********
        createdAt:
          type: string
          format: date-time
          description: The date and time at which the element was created.
          example: '2022-12-07T18:00:39.000Z'
        name:
          type: string
          description: The folder's name.
          example: Billing
        description:
          type: string
          description: The folder's description.
          example: The Billing API.
        type:
          type: string
          description: The folder's type. This is always the `folder` value.
          example: folder
    instanceNotFoundElementFolder:
      title: Not Found
      type: object
      properties:
        error:
          type: object
          properties:
            name:
              type: string
              description: The error name.
              example: instanceNotFoundError
            message:
              type: string
              description: The error message.
              example: The specified API does not exist.
    updatePanApi:
      title: Update API
      type: object
      properties:
        api:
          type: object
          properties:
            parentFolderId:
              type: integer
              description: The API's new parent folder ID.
              example: 1
    updatePanCollection:
      title: Update Collection
      type: object
      properties:
        collection:
          type: object
          properties:
            parentFolderId:
              type: integer
              description: The collection's new parent folder ID.
              example: 1
            environments:
              type: object
              description: The collection's updated environments.
              properties:
                $add:
                  type: array
                  items:
                    type: string
                    description: The ID of environment to add to the collection.
                    example: 92133-622c084d-4f48-470b-826e-18af3c65c775
                $remove:
                  type: array
                  items:
                    type: string
                    description: The ID of environment to remove from the collection.
                    example: 92637-612c7311-4557-470b-826e-18af35682568
    updatePanWorkspace:
      title: Update Workspace
      type: object
      properties:
        workspace:
          type: object
          properties:
            parentFolderId:
              type: integer
              description: The workspace's new parent folder ID.
              example: 1
    updatePanFolder:
      title: Update Folder
      type: object
      properties:
        folder:
          type: object
          properties:
            name:
              type: string
              description: The folder's new name.
              example: Billing
            description:
              type: string
              description: The folder's updated description.
              example: The Billing API.
            parentFolderId:
              type: integer
              description: The folder's new parent folder ID.
              example: 0
    deletePanElementOrFolder:
      title: Successful Response
      type: object
      properties:
        elementType:
          type: object
          description: The Private API Network element type. The name of the object is the element `api`, `collection`, `workspace`, or `folder` type.
          properties:
            id:
              type: string
              description: The element's ID.
              example: 5360b75f-447e-467c-9299-12fd6c92450d
    panFolder400ErrorNotEmpty:
      title: Folder Not Empty
      type: object
      properties:
        name:
          type: string
          description: The error name.
          example: instanceNotFoundError
        message:
          type: string
          description: The error message.
          example: You need to empty this folder before deleting it.
    getAllPanAddElementRequests:
      title: Successful Response
      type: object
      properties:
        requests:
          description: Information about the requests to add elements to your team's Private API Network.
          type: array
          items:
            type: object
            properties:
              id:
                type: integer
                description: The request's ID.
                example: 2
              createdAt:
                type: string
                format: date-time
                description: The date and time at which the request was created.
                example: '2022-06-09T14:48:45.000Z'
              createdBy:
                type: integer
                description: The ID of the user who created the request.
                example: ********
              message:
                type: string
                description: The user's optional message included in the request.
                example: Please approve this collection
              status:
                type: string
                description: The request's status.
                enum:
                  - pending
                  - denied
                example: denied
              element:
                type: object
                description: Information about the requested element.
                properties:
                  id:
                    type: string
                    description: The element's ID.
                    example: 5360b75f-447e-467c-9299-12fd3c92450d
                  type:
                    type: string
                    description: The element type.
                    enum:
                      - api
                      - workspace
                      - collection
                    example: collection
                  name:
                    type: string
                    description: The element's name.
                    example: Test Collection
                  summary:
                    type: string
                    nullable: true
                    description: If applicable, the element's short summary.
                    example: null
              response:
                description: Information about the response to the request. This object only returns when the API Network Manager denied a request with a message.
                type: object
                properties:
                  createdAt:
                    type: string
                    format: date-time
                    description: The date and time at which the API Network Manager denied the request.
                    example: '2020-06-01T08:32:31.000Z'
                  createdBy:
                    type: integer
                    description: The API Network Manager's user ID.
                    example: 2272
                  message:
                    type: string
                    description: The API Network Manager's request response message.
                    example: Too many errors, please fix and resubmit
        meta:
          type: object
          description: The response's non-standard meta information.
          properties:
            limit:
              type: integer
              description: The maximum number of items returned.
              example: 10
            offset:
              type: integer
              description: The zero-based offset of the first item returned.
              example: 5
            totalCount:
              type: integer
              description: The total count of items found.
              example: 100
    pan400ErrorInvalidQueryParams:
      title: Bad Request
      type: object
      properties:
        name:
          type: string
          description: The error name.
          example: invalidParamsError
        message:
          type: string
          description: The error message.
          example: The specified request does not exist.
    respondPanElementAddRequestBody:
      type: object
      required:
        - status
      properties:
        response:
          type: object
          description: The response to the user's request.
          properties:
            message:
              type: string
              description: A message that details why the user's request was denied.
              example: The requested collection has a lot of governance violations. Please fix them.
        status:
          type: string
          description: The request's status.
          enum:
            - denied
            - approved
          example: denied
    respondPanElementAddRequestResponse:
      title: Successful Response
      type: object
      properties:
        request:
          type: array
          description: Information about the Private API Network request.
          items:
            type: object
            properties:
              id:
                type: integer
                description: The request's ID.
                example: 2
              createdAt:
                type: string
                format: date-time
                description: The date and time at which the request was created.
                example: '2022-06-09T14:48:45.000Z'
              createdBy:
                type: integer
                description: The ID of the user who created the request.
                example: ********
              message:
                type: string
                description: The user's optional message included in the request.
                example: Please add this element to the network
              response:
                description: Information about the response to the element's request. This object only returns when the API Network Manager denied a request with a message.
                type: object
                properties:
                  createdAt:
                    type: string
                    format: date-time
                    description: The date and time at which the API Network Manager denied the request.
                    example: '2020-06-01T08:32:31.000Z'
                  createdBy:
                    type: integer
                    description: The API Network Manager's user ID.
                    example: 87654321
                  message:
                    type: string
                    description: The API Network Manager's request response message.
                    example: Please fix the API issues first
              element:
                description: Information about the requested element.
                type: object
                properties:
                  id:
                    type: string
                    description: The element's ID.
                    example: 5360b75f-447e-467c-9299-12fd3c92450d
                  createdAt:
                    type: string
                    format: date-time
                    description: The date and time at which the element was approved and added to the Private API Network.
                    example: '2022-09-28T13:48:09.000Z'
                  createdBy:
                    type: integer
                    description: The user ID of the user who requested to add the element to the Private API Network.
                    example: ********
                  type:
                    type: string
                    description: The element type.
                    enum:
                      - api
                      - workspace
                      - collection
                    example: api
                  name:
                    type: string
                    description: The element's name.
                    example: Test API
                  summary:
                    type: string
                    nullable: true
                    description: If applicable, the element's short summary.
                    example: This is a test API
              status:
                type: string
                description: The request's status.
                enum:
                  - approved
                  - denied
                example: denied
    pan400ErrorInvalidParams:
      title: Bad Request
      type: object
      properties:
        name:
          type: string
          description: The error name.
          example: invalidParamsError
        message:
          type: string
          description: The error message.
          example: Status type can only be denied or approved.
    pullRequestGet:
      title: Successful Response
      type: object
      properties:
        createdAt:
          type: string
          description: The date and time at which the pull request was created.
          example: '2024-02-20T09:55:46.000Z'
        updatedAt:
          type: string
          description: The date and time at which the pull request was updated.
          example: '2024-02-20T09:58:38.000Z'
        id:
          type: string
          description: The pull request's ID.
          example: 4e1a6609-1a29-4037-a411-89ecc14c6cd8
        title:
          type: string
          description: The pull request's title.
          example: Test PR
        description:
          type: string
          description: The pull request's description.
          example: This is a test pull request.
        createdBy:
          type: string
          description: The ID of the user who created the pull request.
          example: '********'
        updatedBy:
          type: string
          description: The ID of the user who last updated the pull request.
          example: '********'
        comment:
          type: string
          description: If the pull request is a `decline` status, an optoinal comment about why the pull request was declined.
          example: Missing descriptions in requests
        fortkType:
          type: string
          description: The type of element the pull request was forked from.
          example: collection
        source:
          type: object
          description: Information about the pull request's source (parent) element.
          properties:
            id:
              type: string
              description: The pull request's source ID.
              example: 87654321-3b79068c-dbe5-41d5-a826-51be4bf646ef
            name:
              type: string
              description: The source element's name.
              example: test-collection
            forkName:
              type: string
              description: The name of the fork created from the source element.
              example: Taylor Lee's fork
            exists:
              type: boolean
              description: If true, whether the element is present and not deleted.
              example: true
        destination:
          description: Information about the pull request destination element.
          type: object
          properties:
            id:
              type: string
              description: The destination element's ID.
              example: ********9-24f57217-1169-4b7c-a810-0e957c04eaa5
            name:
              type: string
              description: The destination element's name.
              example: test-collection
            exists:
              type: boolean
              description: If true, whether the element is present and not deleted.
              example: true
        status:
          type: string
          description: |
            The pull request's current review status:
            - `open` — The pull request is still open.
            - `approved` — The pull request was approved by its reviewers.
            - `declined` — The pull request was not approved by its reviewers.
          example: declined
        merge:
          type: object
          description: Information about the current progress of the pull request's merge.
          properties:
            status:
              type: string
              description: |
                The pull request's current merge status:
                - `inactive` — There is no merge in progress.
                - `inprogress` — The pull request is currently merging.
                - `failed` — The pull request's merge failed.
              enum:
                - inactive
                - inprogress
                - failed
              example: inactive
        reviewers:
          type: array
          description: Information about the reviewers assigned to the pull request.
          items:
            type: object
            properties:
              id:
                type: string
                description: The reviewer's user ID.
                example: '********'
              status:
                type: string
                description: |
                  The reviewer's review status response. One of:
                  - `approved`
                  - `declined`
                example: declined
    pullRequestUpdate:
      title: Update Pull Request
      type: object
      required:
        - reviewers
        - title
      properties:
        title:
          type: string
          description: The pull request's updated title.
          example: Updated PR title
        description:
          type: string
          description: The updated pull request description.
          example: Updated description of the pull request.
        reviewers:
          type: array
          description: An updated list of the pull request's assigned reviewers. This replaces all existing users assigned to the pull request with those you pass in the request body.
          items:
            type: string
            description: The reviewer's user ID.
            example: '********'
    pullRequestUpdated:
      title: Pull Request Updated
      type: object
      properties:
        createdAt:
          type: string
          description: The date and time at which the pull request was created.
          example: '2024-02-20T09:58:57.000Z'
        createdBy:
          type: string
          description: The ID of the user who created the pull request.
          example: '********'
        description:
          type: string
          description: The pull request's description.
          example: Updated description of the pull request.
        destinationId:
          type: string
          description: The pull request's merge destination ID.
          example: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
        forkType:
          type: string
          description: The type of forked element.
          example: collection
        id:
          type: string
          description: The pull request's ID.
          example: 4e1a6609-1a29-4037-a411-89ecc14c6cd8
        sourceId:
          type: string
          description: The unique ID of the source element.
          example: 87654321-3b79068c-dbe5-41d5-a826-51be4bf646ef
        status:
          type: string
          description: The pull request's status.
          enum:
            - open
            - approved
            - declined
            - merged
          example: open
        title:
          type: string
          description: The pull request's title.
          example: Updated PR title
        updatedAt:
          type: string
          description: The date and time at which the pull request was updated.
          example: '2024-02-20T09:58:57.000Z'
    pullRequest409ErrorConflict:
      title: Conflict
      type: object
      properties:
        detail:
          type: string
          description: Details about the occurrence of the error.
          example: You cannot modify a pull request which is already merged or declined.
        status:
          type: number
          format: http-status-code
          description: The error's HTTP status code.
          example: 409
        title:
          type: string
          description: A short summary of the error.
          example: Conflict
        type:
          type: string
          format: uri
          description: The error type.
          example: https://api.postman.com/problems/conflict
    pullRequestReview:
      title: Review Pull Request
      type: object
      required:
        - action
      properties:
        action:
          type: string
          description: |
            The action to perform on the pull request:
            - `approve` — Approve the pull request for merge.
            - `decline` — Decline the pull request.
            - `merge` — Merge the pull request into its parent element.
            - `unapprove` — Revokes a pull request's `approve` status. This action does not decline the pull request.
          enum:
            - approve
            - decline
            - merge
            - unapprove
          example: decline
        comment:
          type: string
          description: If the pull request is a `decline` status, an optoinal comment about why the pull request was declined.
          example: Missing descriptions in requests
    pullRequestReviewReponse:
      title: Successful Response
      type: object
      properties:
        id:
          type: string
          description: The pull request's ID.
          example: 4e1a6609-1a29-4037-a411-89ecc14c6cd8
        reviewedBy:
          type: object
          description: Information about the user who reviewed the pull request review.
          properties:
            id:
              type: number
              description: The user's ID.
              example: ********
            name:
              type: string
              description: The user's name.
              example: Taylor Lee
            username:
              type: string
              description: The user's username.
              example: taylor-lee
        status:
          type: string
          description: |
            The pull request's current review status:
            - `open` — The pull request is still open.
            - `approved` — The pull request was approved by its reviewers.
            - `declined` — The pull request was not approved by its reviewers.
          example: approved
        updatedAt:
          type: string
          description: The date and time at which the pull request was updated.
          example: '2024-02-21T08:19:09.000Z'
    pullRequestReview400ErrorActionNotAllowed:
      title: Action Not Allowed
      type: object
      properties:
        type:
          type: string
          format: uri
          description: The error type.
          example: https://api.postman.com/problems/bad-request
        title:
          type: string
          description: A short summary of the error.
          example: Bad Request
        detail:
          type: string
          description: Details about the occurrence of the error.
          example: Given action on current pull request is not allowed
        status:
          type: number
          format: http-status-code
          description: The error's HTTP status code.
          example: 400
    pullRequestReview400ErrorInvalidAction:
      title: Invalid Action
      type: object
      properties:
        type:
          type: string
          format: uri
          description: The error type.
          example: https://api.postman.com/problems/bad-request
        title:
          type: string
          description: A short summary of the error.
          example: Value must be one of "approve", "unapprove", "decline", "merge"
        detail:
          type: string
          description: Details about the occurrence of the error.
          example: 'POST request body for ''/pull-requests/4e1a6609-1a29-4037-a411-89ecc14c6cd8/tasks'' failed to validate schema. Location: /properties/action/enum'
        status:
          type: number
          format: http-status-code
          description: The error's HTTP status code.
          example: 400
    getSecretTypes:
      title: Successful Response
      type: object
      properties:
        data:
          type: array
          items:
            type: object
            description: Information about the secret type.
            properties:
              name:
                type: string
                description: The name of the secret type.
                example: Postman Collection Key
              id:
                type: string
                description: The ID of the secret type.
                example: 04084949-0dda-4735-ace5-f83038f7b200
              type:
                type: string
                description: |
                  The origin of the secret type:
                  - `DEFAULT` — Supported by default in Postman.
                  - `TEAM_REGEX` — A custom regex added by an Admin or Super Admin user in the **Configure Alerts** section of the [**Team Settings**](https://learning.postman.com/docs/administration/team-settings/) interface.
                enum:
                  - DEFAULT
                  - TEAM_REGEX
                example: TEAM_REGEX
        meta:
          type: object
          properties:
            total:
              type: integer
              description: The total number of supported secrets.
              example: 4
    schemaSecurityValidationRequest:
      title: Schema Validation Request Body
      type: object
      properties:
        schema:
          type: object
          required:
            - language
            - schema
            - type
          properties:
            language:
              type: string
              description: The definition format.
              enum:
                - json
                - yaml
              example: json
            schema:
              type: string
              description: The stringified API definition.
              example: '{"openapi":"3.0.0","info":{"version":"1","title":"temp","license":{"name":"MIT"}},"servers":[{"url":"https://petstore.swagger.io/v1"}],"paths":{"/user":{"get":{"summary":"Details about a user","operationId":"listUser","tags":["user"],"parameters":[{"name":"id","in":"query","description":"ID of the user","required":true,"schema":{"type":"integer","format":"int32"}}],"responses":{"200":{"description":"Details about a user","headers":{"x-next":{"description":"A link to the next page of responses","schema":{"type":"string"}}},"content":{"application/json":{"schema":{$ref:"#/components/schemas/User"}}}},"default":{"description":"unexpected error","content":{"application/json":{"schema":{$ref:"#/components/schemas/Error"}}}}}}}},"components":{"schemas":{"User":{"type":"object","required":["id","name"],"properties":{"id":{"type":"integer","format":"int64"},"name":{"type":"string"},"tag":{"type":"string"}}},"Error":{"type":"object","required":["code","message"],"properties":{"code":{"type":"integer","format":"int32"},"message":{"type":"string"}}}},"securitySchemes":{"BasicAuth":{"type":"http","scheme":"basic"}}},"security":[{"BasicAuth":[]}]}'
            type:
              type: string
              description: The definition type.
              enum:
                - openapi3
                - openapi2
              example: openapi3
    schemaSecurityValidationResponse:
      title: Successful Response
      type: object
      properties:
        warnings:
          type: array
          description: |
            Information about each issue discovered in the analysis. Each object includes the violation's severity and category, the location of the issue, data paths, and other information. This returns an empty object if there are no issues present in the schema.

            If there are issues, this returns the `possibleFixUrl` response in each warning object. This provides a link to documentation you can use to resolve the warning.
          items:
            type: object
            additionalProperties: true
    schemaSecurityValidation400Error:
      title: Invalid Schema
      type: object
      properties:
        error:
          type: object
          properties:
            name:
              type: object
              properties:
                name:
                  type: string
                  description: The error name.
                  example: Invalid schema
                message:
                  type: string
                  description: The error message.
                  example: Provided schema type is not supported.
    getTaggedEntities:
      title: Successful Response
      type: object
      properties:
        data:
          type: object
          description: An object containing the paginated elements.
          required:
            - entities
          properties:
            entities:
              type: array
              description: A list of the Postman elements that contain the given tag.
              items:
                type: object
                properties:
                  entityId:
                    type: string
                    format: uid
                    description: The element's unique ID.
                    example: ********-6fd634a3-79ba-451d-8f07-56a953f96667
                  entityType:
                    enum:
                      - api
                      - collection
                      - workspace
                    type: string
                    description: The type of Postman element.
                    example: collection
        meta:
          type: object
          description: The response's pagination information.
          required:
            - count
          properties:
            count:
              type: integer
              description: The number of tagged elements returned in the response.
              example: 2
            nextCursor:
              type: string
              format: base64
              description: The pagination cursor that points to the next record in the results set.
              example: eyJpZCI6ODYsImVudGl0eVR5cGUiOiJhcGkifQ==
    tagElement400Error:
      title: Bad Request
      type: object
      properties:
        error:
          type: string
          description: A short summary of the problem.
          example: Bad Request
        message:
          type: string
          description: The error message.
          example: params/slug must match pattern \"^[a-z][a-z0-9-]*[a-z0-9]+$\"
        statusCode:
          type: integer
          format: http-status-code
          description: The error's HTTP status code.
          example: 400
    createWebhookRequest:
      title: Create Webhook Request Body
      type: object
      properties:
        webhook:
          type: object
          required:
            - name
            - collection
          properties:
            collection:
              type: string
              format: uid
              description: The unique ID of the collection to trigger when calling this webhook.
              example: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
            environment:
              type: string
              format: uid
              description: The unique ID of the environment to trigger when calling this webhook.
              example: ********-d9c7dc8f-904e-4bba-99b5-4d490aae1957
            name:
              type: string
              description: The webhook's name. On success, the system creates a new monitor with this name in the **Monitors** tab.
              example: Test Webhook
    createWebhookResponse:
      title: Successful Response
      type: object
      properties:
        webhook:
          type: object
          properties:
            id:
              type: string
              description: The webhook's ID.
              example: 1f0df51a-8658-4ee8-a2a1-d2567dfa09a9
            name:
              type: string
              description: The webhook's name.
              example: Test Webhook
            collection:
              type: string
              format: uid
              description: The unique ID of the collection that triggers when calling this webhook.
              example: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
            webhookUrl:
              type: string
              description: The webhook's URL.
              example: https://newman-api.getpostman.com/run/********/267a6e99-b6da-407c-a96f-03be2d6282fb
            uid:
              type: string
              format: uid
              description: The webhook's unique ID.
              example: ********-1f0df51a-8658-4ee8-a2a1-d2567dfa09a9
    createWebhookParamMissing400Error:
      type: object
      properties:
        error:
          type: object
          properties:
            name:
              type: string
              description: The error name.
              example: paramMissingError
            message:
              type: string
              description: The error message.
              example: Parameter is missing in the request.
            details:
              type: object
              description: Information about the error.
              properties:
                param:
                  type: array
                  description: Information about the missing parameter.
                  items:
                    type: string
                  example:
                    - collection
    getWorkspaces:
      title: Successful Response
      type: object
      properties:
        workspaces:
          type: array
          items:
            type: object
            description: Information about the workspace.
            properties:
              id:
                type: string
                description: The workspace's ID.
                example: 1f0df51a-8658-4ee8-a2a1-d2567dfa09a9
              name:
                type: string
                description: The workspace's name.
                example: Team Workspace
              type:
                type: string
                description: The type of workspace.
                enum:
                  - personal
                  - team
                  - private
                  - public
                  - partner
                example: team
              visibility:
                type: string
                description: |
                  The workspace's visibility. [Visibility](https://learning.postman.com/docs/collaborating-in-postman/using-workspaces/managing-workspaces/#changing-workspace-visibility) determines who can access the workspace:
                  - `personal` — Only you can access the workspace.
                  - `team` — All team members can access the workspace.
                  - `private` — Only invited team members can access the workspace ([**Professional** and **Enterprise** plans only](https://www.postman.com/pricing)).
                  - `public` — Everyone can access the workspace.
                  - `partner` — Only invited team members and [partners](https://learning.postman.com/docs/collaborating-in-postman/using-workspaces/partner-workspaces/) can access the workspace ([**Professional** and **Enterprise** plans only](https://www.postman.com/pricing)).
                enum:
                  - personal
                  - team
                  - private
                  - public
                  - partner
                example: team
              createdBy:
                type: integer
                description: The user who created the workspace. The response only returns workspaces that you have access to.
                example: ********
              scim:
                type: object
                description: An object containing SCIM user IDs. This object only returns if you pass the `include=scim` query parameter.
                properties:
                  createdBy:
                    type: string
                    description: The SCIM user ID of the user who created the workspace.
                    example: 405775fe15ed41872a8eea4c8aa2b38cda9749812cc55c99
    createWorkspace:
      title: Create Workspace
      type: object
      properties:
        workspace:
          type: object
          description: Information about the workspace.
          required:
            - name
            - type
          properties:
            name:
              type: string
              description: The workspace's name.
              example: Team Workspace
            type:
              type: string
              description: |
                The type of workspace:
                - `personal`
                - `private` — Private workspaces are available on Postman [**Professional** and **Enterprise** plans](https://www.postman.com/pricing).
                - `public`
                - `team`
                - `partner` — [Partner Workspaces](https://learning.postman.com/docs/collaborating-in-postman/using-workspaces/partner-workspaces/) are available on Postman [**Professional** and **Enterprise** plans](https://www.postman.com/pricing)).
              enum:
                - personal
                - private
                - public
                - team
                - partner
              example: team
            description:
              type: string
              description: The workspace's description.
              example: This is a team workspace.
    createWorkspaceResponse:
      title: Successful Response
      type: object
      properties:
        workspace:
          type: object
          description: Information about the created workspace.
          properties:
            id:
              type: string
              description: The workspace's ID.
              example: 1f0df51a-8658-4ee8-a2a1-d2567dfa09a9
            name:
              type: string
              description: The workspace's name.
              example: Test Workspace
    workspace400ErrorMalformedRequest:
      title: Workspace Not Found
      type: object
      properties:
        name:
          type: string
          description: The error name.
          example: malformedRequestError
        message:
          type: string
          description: The error message.
          example: '''workspace'' object missing in the request'
    workspace403ErrorUnauthorized:
      title: Forbidden
      type: object
      properties:
        error:
          type: object
          properties:
            status:
              type: number
              format: http-status-code
              description: The error's HTTP status code.
              example: 403
            name:
              type: string
              description: The error name.
              example: forbiddenError
            message:
              type: string
              description: The error message.
              example: You are not authorized to perform this action
            detail:
              type: string
              description: Information about the error.
              example: You do not have permission to create team workspaces.
            instance:
              type: string
              description: The URI reference that identifies the specific occurrence of the problem.
              example: ''
    getAllWorkspaceRoles:
      title: Successful Response
      type: object
      properties:
        roles:
          type: object
          description: Information about the workspace's [user roles](https://learning.postman.com/docs/collaborating-in-postman/roles-and-permissions/#team-roles).
          properties:
            user:
              type: array
              description: The list of user roles in the workspace.
              items:
                type: object
                description: Information about the user role.
                properties:
                  id:
                    type: string
                    description: The role's ID.
                    example: '1'
                  description:
                    type: string
                    description: The role's description.
                    example: Can manage people and all resources
                  displayName:
                    type: string
                    description: The role's display name.
                    example: Admin
            usergroup:
              type: array
              description: Information about the workspace's [user group roles](https://learning.postman.com/docs/collaborating-in-postman/user-groups/).
              items:
                type: object
                description: Information about the user group in the workspace.
                properties:
                  id:
                    type: string
                    description: The role's ID.
                    example: '1'
                  description:
                    type: string
                    description: The role's description.
                    example: Can manage people and all resources
                  displayName:
                    type: string
                    description: The role's display name.
                    example: Admin
    getWorkspace:
      title: Successful Response
      type: object
      properties:
        workspace:
          type: object
          description: Information about the workspace.
          properties:
            id:
              type: string
              description: The workspace's ID.
              example: 1f0df51a-8658-4ee8-a2a1-d2567dfa09a9
            name:
              type: string
              description: The workspace's name.
              example: Team Workspace
            type:
              type: string
              description: The type of workspace.
              enum:
                - personal
                - team
                - private
                - public
                - partner
              example: team
            description:
              type: string
              description: The workspace's description.
              example: This is a team workspace.
            visibility:
              type: string
              description: |
                The workspace's visibility. [Visibility](https://learning.postman.com/docs/collaborating-in-postman/using-workspaces/managing-workspaces/#changing-workspace-visibility) determines who can access the workspace:
                - `personal` — Only you can access the workspace.
                - `team` — All team members can access the workspace.
                - `private` — Only invited team members can access the workspace ([**Professional** and **Enterprise** plans only](https://www.postman.com/pricing)).
                - `public` — Everyone can access the workspace.
                - `partner` — Only invited team members and [partners](https://learning.postman.com/docs/collaborating-in-postman/using-workspaces/partner-workspaces/) can access the workspace ([**Professional** and **Enterprise** plans only](https://www.postman.com/pricing)).
              enum:
                - personal
                - team
                - private
                - public
                - partner
              example: team
            createdBy:
              type: string
              description: The user ID of the user who created the workspace.
              example: '********'
            updatedBy:
              type: string
              description: The user ID of the user who last updated the workspace.
              example: '********'
            createdAt:
              type: string
              format: date-time
              description: The date and time at which the workspace was created.
              example: '2022-07-06T16:18:32.000Z'
            updatedAt:
              type: string
              format: date-time
              description: The date and time at which the workspace was last updated.
              example: '2022-07-06T20:55:13.000Z'
            collections:
              type: array
              description: The workspace's collections.
              items:
                type: object
                description: Information about the collection.
                properties:
                  id:
                    type: string
                    description: The collection's ID.
                    example: 12ece9e1-2abf-4edc-8e34-de66e74114d2
                  name:
                    type: string
                    description: The collection's name.
                    example: Test Collection
                    deprecated: true
                  uid:
                    type: string
                    format: uid
                    description: The collection's unique ID.
                    example: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
                    deprecated: true
            environments:
              type: array
              description: The workspace's environments.
              items:
                type: object
                description: Information about the environment.
                properties:
                  id:
                    type: string
                    description: The environment's ID.
                    example: 5daabc50-8451-43f6-922d-96b403b4f28e
                  name:
                    type: string
                    description: The environment's name.
                    example: Test Environment
                    deprecated: true
                  uid:
                    type: string
                    format: uid
                    description: The environment's unique ID.
                    example: ********-5daabc50-8451-43f6-922d-96b403b4f28e
                    deprecated: true
            mocks:
              type: array
              description: The workspace's mock servers.
              items:
                type: object
                description: Information about the mock server.
                properties:
                  id:
                    type: string
                    description: The mock server's ID.
                    example: e3d951bf-873f-49ac-a658-b2dcb91d3289
                  name:
                    type: string
                    description: The mock server's name.
                    example: Test Mock
                    deprecated: true
                  uid:
                    type: string
                    format: uid
                    description: The mock server's unique ID.
                    example: ********-e3d951bf-873f-49ac-a658-b2dcb91d3289
                    deprecated: true
                  deactivated:
                    type: boolean
                    description: If true, the mock server is not active. Mock servers deactivate when a linked collection or environment is deleted.
                    example: false
            monitors:
              type: array
              description: The workspace's monitors.
              items:
                type: object
                description: Information about the monitor.
                properties:
                  id:
                    type: string
                    description: The monitor's ID.
                    example: 1e6b6cc1-c760-48e0-968f-4bfaeeae9af1
                  name:
                    type: string
                    description: The monitor's name.
                    example: Test Environment
                    deprecated: true
                  uid:
                    type: string
                    format: uid
                    description: The monitor's unique ID.
                    example: ********-1e6b6cc1-c760-48e0-968f-4bfaeeae9af1
                    deprecated: true
                  deactivated:
                    type: boolean
                    description: If true, the monitor is not active.
                    example: false
            apis:
              type: array
              description: The workspace's APIs.
              items:
                type: object
                description: Information about the API.
                properties:
                  id:
                    type: string
                    description: The API's ID.
                    example: 387c2863-6ee3-4a56-8210-225f774edade
                  name:
                    type: string
                    description: The API's name.
                    example: Test API
                    deprecated: true
                  uid:
                    type: string
                    format: uid
                    description: The API's unique ID.
                    example: ********-387c2863-6ee3-4a56-8210-225f774edade
                    deprecated: true
            scim:
              type: object
              description: An object containing SCIM user IDs. This object only returns if you pass the `include=scim` query parameter.
              properties:
                createdBy:
                  type: string
                  description: The SCIM user ID of the user who created the workspace.
                  example: 405775fe15ed41872a8eea4c8aa2b38cda9749812cc55c99
                updatedBy:
                  type: string
                  description: The SCIM user ID of the user who last updated the workspace.
                  example: 405775fe15ed41872a8eea4c8aa2b38cda9749812cc55c99
    workspace404ErrorNotFound:
      title: Not Found
      type: object
      properties:
        error:
          type: object
          properties:
            name:
              type: string
              description: The error name.
              example: workspaceNotFoundError
            message:
              type: string
              description: The error message.
              example: Workspace not found
            statusCode:
              type: number
              format: http-status-code
              description: The error's HTTP status code.
              example: 404
    updateWorkspace:
      title: Update Workspace
      type: object
      properties:
        workspace:
          type: object
          properties:
            name:
              type: string
              description: The workspace's new name.
              example: Test Workspace
            type:
              type: string
              description: |
                The new workspace visibility [type](https://learning.postman.com/docs/collaborating-in-postman/using-workspaces/managing-workspaces/#changing-workspace-visibility). This property does not support the following workspace visibility changes:
                - `private` to `public`, `public` to `private`, and `private` to `personal` for Free and Basic [plans](https://www.postman.com/pricing/).
                - `public` to `personal` for team users.
              enum:
                - private
                - personal
                - team
                - public
              example: team
            description:
              type: string
              description: The new workspace description.
              example: This is a test team workspace.
    updateWorkspaceResponse:
      title: Workspace Updated
      type: object
      properties:
        workspace:
          type: object
          description: Information about the updated workspace.
          properties:
            id:
              type: string
              description: The workspace's ID.
              example: 1f0df51a-8658-4ee8-a2a1-d2567dfa09a9
            name:
              type: string
              description: The workspace's name.
              example: Test Workspace
    workspace403Error:
      title: Forbidden
      type: object
      properties:
        error:
          type: object
          properties:
            name:
              type: string
              description: The error name.
              example: forbiddenError
            message:
              type: string
              description: The error message.
              example: You do not have access to update this workspace.
    instanceNotFoundWorkspace:
      title: Instance Not Found
      type: object
      properties:
        error:
          type: object
          properties:
            name:
              type: string
              description: The error name.
              example: instanceNotFoundError
            message:
              type: string
              description: The error message.
              example: The specified workspace does not exist.
    deleteWorkspace:
      title: Workspace Deleted
      type: object
      properties:
        workspace:
          type: object
          description: Information about the deleted workspace.
          properties:
            id:
              type: string
              description: The workspace's ID.
              example: 1f0df51a-8658-4ee8-a2a1-d2567dfa09a9
    workspace400Error:
      title: Not Found
      type: object
      properties:
        error:
          type: object
          properties:
            name:
              type: string
              description: The error name.
              example: instanceNotFoundError
            message:
              type: string
              description: The error message.
              example: The specified workspace does not exist.
    globalVariable:
      title: Global Variable Info
      type: object
      description: Information about the global variable.
      properties:
        key:
          type: string
          description: The variable's name.
          example: variableName
        type:
          type: string
          description: The [type](https://learning.postman.com/docs/sending-requests/variables/#variable-types) of variable.
          enum:
            - default
            - secret
          example: default
        value:
          type: string
          description: The variable's value.
          example: variableValue
        enabled:
          type: boolean
          description: If true, the variable is enabled.
          example: true
    getWorkspaceGlobalVariables:
      title: Successful Response
      type: object
      description: Information about the workspace's global variables.
      properties:
        values:
          type: array
          description: A list of the workspace's global variables.
          items:
            $ref: '#/components/schemas/globalVariable'
    globalVariables500Error:
      title: Internal Server Error
      type: object
      properties:
        type:
          type: string
          format: uri-reference
          description: The [URI reference](https://www.rfc-editor.org/rfc/rfc3986) that identifies the type of problem.
          example: https://api.postman.com/problems/servererror
        title:
          type: string
          description: A short summary of the problem.
          example: Internal server error
        detail:
          type: string
          description: Information about the error.
          example: Details about the error
    updateWorkspaceGlobalVariables:
      title: Update Global Variables
      type: object
      properties:
        values:
          type: array
          description: A list of the workspace's global variables.
          items:
            $ref: '#/components/schemas/globalVariable'
    updateWorkspaceGlobalVariablesResponse:
      title: Global Variables Updated
      type: object
      description: Information about the workspace's updated global variables.
      properties:
        values:
          type: array
          description: A list of the workspace's global variables.
          items:
            $ref: '#/components/schemas/globalVariable'
    getWorkspaceRoles:
      title: Workspace Roles
      type: object
      description: Information about the workspace's roles.
      properties:
        roles:
          type: array
          description: A list of workspace roles and the users and groups assigned to them.
          items:
            type: object
            properties:
              id:
                type: string
                description: The role's ID.
                example: '3'
              user:
                type: array
                description: A list of user IDs assigned to the role.
                items:
                  type: string
                  description: The user's ID or SCIM ID.
                  example: '********'
              group:
                type: array
                description: A list of user group IDs assigned to the role.
                items:
                  type: string
                  description: The group's ID.
                  example: '123'
              displayName:
                type: string
                description: The role's display name.
                enum:
                  - Admin
                  - Viewer
                  - Editor
                example: Viewer
    updateWorkspaceRoles:
      title: Update Workspace Roles
      type: object
      properties:
        roles:
          type: array
          items:
            type: object
            required:
              - op
              - path
              - value
            properties:
              op:
                type: string
                description: The operation to perform on the path.
                example: add
              path:
                type: string
                description: The resource to perform the action on.
                enum:
                  - /user
                  - /usergroup
                example: /user
              value:
                type: array
                items:
                  type: object
                  required:
                    - id
                    - role
                  description: Information about the updated user or user group role.
                  properties:
                    id:
                      type: string
                      description: The user or user group ID. To use SCIM IDs for users or groups, include the `identifierType=scim` header in the request.
                      example: '********'
                    role:
                      type: string
                      description: |
                        The workspace role's ID:
                        - `1` — Viewer. Can view, fork, and export workspace resources.
                        - `2` — Editor. Can create and edit workspace resources.
                        - `3` — Admin. Can manage workspace details and members.
                      enum:
                        - '1'
                        - '2'
                        - '3'
                      example: '1'
    updateWorkspaceRolesResponse:
      title: Workspace Roles Updated
      type: object
      properties:
        roles:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
                description: The role's ID.
                example: '1'
              user:
                type: array
                description: A list of user IDs assigned to the role.
                items:
                  type: string
                  description: The user's ID or SCIM ID.
                  example: '********'
              group:
                type: array
                description: A list of user group IDs assigned to the role.
                items:
                  type: string
                  description: The user group's ID or SCIM ID.
                  example: 561631fq14ed41872a8eea4c8aa2b38cda9749812cc55c00
              displayName:
                type: string
                description: The role's display name.
                enum:
                  - Admin
                  - Viewer
                  - Editor
                example: Viewer
    workspaceRoles400Error:
      title: Bad Request
      type: object
      properties:
        type:
          type: string
          description: The error type.
          example: invalidParamError
        title:
          type: string
          description: A short summary of the problem.
          example: body.roles[0] should have required property 'op'
        detail:
          type: string
          description: Information about the error.
          example: ''
        status:
          type: number
          format: http-status-code
          description: The error's HTTP status code.
          example: 400
    workspaceRoles422UnsupportRoleError:
      title: Partner and Personal Workspace Roles Unsupported
      type: object
      properties:
        detail:
          type: string
          description: Information about the error.
          example: Roles are not supported for personal and partner workspaces.
        link:
          type: string
          description: The error type.
          example: https://api.postman.com/problems/unprocessable-entity
        status:
          type: number
          format: http-status-code
          description: The error's HTTP status code.
          example: 422
        title:
          type: string
          description: A short summary of the problem.
          example: Cannot process the request.
