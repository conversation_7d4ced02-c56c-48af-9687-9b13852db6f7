responses:
    getAccounts:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/invoicesAccountInfo'
          example:
            billingEmail: <EMAIL>
            id: 372691
            salesChannel: SELF_SERVE
            slots:
              available: 8
              consumed: 2
              total: 10
              unbilled: 0
            state: PAID
            teamId: 380691
    invoicesNoTeam400Error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/invoicesNoTeam400Error'
          example:
            type: https://api.postman.com/problems/bad-request
            title: Malformed request
            status: 400
            detail: You must be part of a team
    unauthorizedError:
      description: Unauthorized
      content:
        application/problem+json:
          schema:
            type: object
            properties:
              type:
                type: string
                format: uri-reference
                description: The [URI reference](https://www.rfc-editor.org/rfc/rfc3986) that identifies the type of problem.
                example: https://api.postman.com/problems/unauthorized
              title:
                type: string
                description: A short summary of the problem.
                example: Unauthorized
              detail:
                type: string
                description: Information about the error.
                example: An API key must be provided in the request header or query string
              status:
                type: number
                format: http-status-code
                description: The error's HTTP status code.
                example: 401
          example:
            type: https://api.postman.com/problems/unauthorized
            title: Unauthorized
            detail: An API key must be provided in the request header or query string
            status: 401
    common500ErrorServerError:
      description: Internal Server Error
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                type: object
                properties:
                  name:
                    type: string
                    description: The error name.
                    example: serverError
                  message:
                    type: string
                    description: The error message.
                    example: An error has occurred on the server.
          example:
            error:
              name: serverError
              message: An error has occurred on the server.
    getAccountInvoices:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/getAccountInvoices'
          example:
            data:
              - id: inv_7UDSYBJPGQU93N7M
                status: PAID
                issuedAt: '2023-10-12'
                totalAmount:
                  value: 440
                  currency: USD
                links:
                  web:
                    href: https://pay.postman.com/invoices/pay?invoice_public_id=inv_7UDSYBJPGQU93N7M
    invoiceMissingStatus400Error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/invoiceMissingStatus400Error'
          example:
            type: https://api.postman.com/problems/bad-request
            title: Malformed request
            status: 400
            detail: Please provide a valid status to fetch invoices
    invoicesForbidden403Error:
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/invoicesForbidden403Error'
          example:
            type: https://api.postman.com/problems/forbidden
            title: Forbidden
            status: 403
            detail: You are not authorized to perform this action
    getApis:
      description: Successful Response
      content:
        application/json:
          schema:
            type: object
            description: Information about the API schema.
            properties:
              apis:
                type: array
                items:
                  type: object
                  title: API Base Data Schema
                  description: The API's base data schema.
                  properties:
                    id:
                      type: string
                      description: The API's ID.
                      example: 5360b75f-447e-467c-9299-12fd6c92450d
                    name:
                      type: string
                      description: The API's name.
                      example: User Management API
                    summary:
                      type: string
                      description: The API's short summary.
                      example: An API to manager users.
                    createdAt:
                      type: string
                      format: date-time
                      description: The date and time at which the API was created.
                      example: '2023-08-22T14:21:57.000Z'
                    createdBy:
                      type: integer
                      description: The Postman ID of the user that created the API.
                      example: ********
                    updatedAt:
                      type: string
                      format: date-time
                      description: The date and time at which the API was updated.
                      example: '2023-08-22T14:21:57.000Z'
                    updatedBy:
                      type: integer
                      description: The Postman ID of the user that updated the API.
                      example: ********
                    description:
                      type: string
                      description: The API's description.
                      example: This API includes operations to add/remove/update users.
              meta:
                type: object
                description: The response's meta information for paginated results.
                properties:
                  limit:
                    type: number
                    example: 100
                    description: The maximum number of records in the paginated response.
                  total:
                    type: number
                    example: 1000
                    description: The number of records that match the defined criteria.
                  nextCursor:
                    type: string
                    format: base64
                    description: The pagination cursor that points to the next record in the results set.
                    example: RnJpIEZlYiAyNCAyMDIzIDEzOjI0OjA5IEdNVCswMDAwIChDb29yZGluYXRlZCBVbml2ZXJzYWwgVGltZSk=
          example:
            meta:
              nextCursor: VGh1IE1hciAxNiAyMDIzIDE3OjIxOjUzIEdNVCswMDAwIChDb29yZGluYXRlZCBVbml2ZXJzYWwgVGltZSk=
              total: 4
              limit: 10
            apis:
              - updatedBy: ********
                createdBy: ********
                name: New API
                summary: ''
                description: ''
                createdAt: '2023-02-15T06:27:16.000Z'
                id: 73e15000-bc7a-4802-b80e-05fff18fd7f8
                updatedAt: '2023-02-15T06:27:24.000Z'
              - updatedBy: ********
                createdBy: ********
                name: Test API
                summary: This is a test API.
                description: This is an API for testing purposes.
                createdAt: '2023-02-15T13:07:08.000Z'
                id: fec65321-5f55-4feb-8525-be95bccae8dd
                updatedAt: '2023-02-15T13:07:08.000Z'
              - updatedBy: ********
                createdBy: ********
                name: Test API
                summary: This is a test API.
                description: This is an API for testing purposes.
                createdAt: '2023-02-16T05:52:27.000Z'
                id: b7c54faa-d119-4572-a18d-e2f5c32348ce
                updatedAt: '2023-02-16T05:52:27.000Z'
              - updatedBy: ********
                createdBy: ********
                name: Test API
                summary: This is a test API.
                description: This is an API for testing purposes.
                createdAt: '2023-02-16T07:07:16.000Z'
                id: d535a8ba-ed4b-4c6a-bf1c-13fac95485b8
                updatedAt: '2023-02-16T07:07:16.000Z'
    common401Error:
      description: Unauthorized
      content:
        application/problem+json:
          schema:
            type: object
            properties:
              type:
                type: string
                format: uri-reference
                description: The [URI reference](https://www.rfc-editor.org/rfc/rfc3986) that identifies the type of problem.
                example: https://api.postman.com/problems/unauthorized
              title:
                type: string
                description: A short summary of the problem.
                example: Unauthorized
              detail:
                type: string
                description: Information about the error.
                example: Unauthorized
              status:
                type: integer
                format: http-status-code
                description: The error's HTTP status code.
                example: 401
          example:
            type: https://api.postman.com/problems/unauthorized
            title: Unauthorized
            detail: Unauthorized
            status: 401
    featureUnavailable403Error:
      description: Feature Unavailable
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/featureUnavailable403Error'
          example:
            value:
              type: https://api.postman.com/problems/forbidden
              title: Forbidden
              detail: This feature isn't available in your region.
              status: 403
    api404ErrorNotFound:
      description: API Not Found
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/api404ErrorNotFound'
          examples:
            API Not Found:
              $ref: '#/components/examples/api404ErrorNotFound'
    v9Unsupported:
      description: v9 Unsupported
      content:
        application/problem+json:
          schema:
            type: object
            properties:
              type:
                type: string
                description: The error type.
                example: unsupportedEntityError
              title:
                type: string
                description: A short summary of the problem.
                example: Unsupported API
              detail:
                type: string
                description: Details about the error.
                example: This endpoint does not support v9 APIs.
          example:
            type: unsupportedEntityError
            title: Unsupported API
            detail: This endpoint does not support v9 APIs.
    common500Error:
      description: Internal Server Error
      content:
        application/problem+json:
          schema:
            type: object
            properties:
              type:
                type: string
                format: uri-reference
                description: The [URI reference](https://www.rfc-editor.org/rfc/rfc3986) that identifies the type of problem.
                example: https://api.postman.com/problems/internal-server-error
              title:
                type: string
                description: A short summary of the problem.
                example: Internal Server Error
              detail:
                type: string
                description: Information about the error.
                example: Internal Server Error
          example:
            type: https://api.postman.com/problems/internal-server-error
            title: Internal Server Error
            detail: Internal Server Error
    createApi:
      description: Successful Response
      content:
        application/json:
          schema:
            type: object
            description: The API's base data schema.
            title: API Base Data Schema
            properties:
              id:
                type: string
                description: The API's ID.
                example: 5360b75f-447e-467c-9299-12fd6c92450d
              name:
                type: string
                description: The API's name.
                example: User Management API
              summary:
                type: string
                description: The API's short summary.
                example: An API to manager users.
              createdAt:
                type: string
                format: date-time
                description: The date and time at which the API was created.
                example: '2023-08-22T14:21:57.000Z'
              createdBy:
                type: integer
                description: The Postman ID of the user that created the API.
                example: ********
              updatedAt:
                type: string
                format: date-time
                description: The date and time at which the API was updated.
                example: '2023-08-22T14:21:57.000Z'
              updatedBy:
                type: integer
                description: The Postman ID of the user that updated the API.
                example: ********
              description:
                type: string
                description: The API's description.
                example: This API includes operations to add/remove/update users.
          example:
            id: 90ca9f5a-c4c4-11ed-afa1-0242ac120002
            name: Test API
            summary: Testing API
            createdAt: '2023-02-17T04:09:01.000Z'
            createdBy: ********
            updatedAt: '2023-02-17T04:09:13.000Z'
            updatedBy: ********
            description: This is a test API.
    workspace400ErrorParamMissing:
      description: Missing Workspace ID
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/workspace400ErrorParamMissing'
          example:
            type: paramMissingError
            title: Parameter is missing in the request.
            detail: Parameter, workspaceId is missing in the request.
    api401ErrorUnauthorized:
      description: Unauthorized
      content:
        application/problem+json:
          schema:
            type: object
            properties:
              type:
                type: string
                format: uri-reference
                description: The [URI reference](https://www.rfc-editor.org/rfc/rfc3986) that identifies the type of problem.
                example: https://api.postman.com/problems/unauthorized
              title:
                type: string
                description: A short summary of the problem.
                example: Unauthorized
              detail:
                type: string
                description: Information about the error.
                example: An API key must be provided in the request header or query string
              status:
                type: number
                format: http-status-code
                description: The error's HTTP status code.
                example: 401
              instance:
                type: string
                description: The URI reference that identifies the specific occurrence of the problem.
                example: /collections/12ece9e1-2abf-4edc-8e34-de66e74114d2/requests/%7B%7BrequestId%7D%7D
          example:
            type: https://api.postman.com/problems/unauthorized
            title: Unauthorized
            detail: An API key must be provided in the request header or query string
            status: 401
            instance: /collections/12ece9e1-2abf-4edc-8e34-de66e74114d2/requests/%7B%7BrequestId%7D%7D
    workspace404Error:
      description: Workspace Not Found
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/workspace404Error'
          example:
            title: We could not find the workspace you are looking for
            detail: We could not find the workspace you are looking for.
            type: instanceNotFoundError
    getApi:
      description: Successful Response
      content:
        application/json:
          schema:
            anyOf:
              - type: object
                title: API Base Data Schema
                description: The API's base data schema.
                properties:
                  id:
                    type: string
                    description: The API's ID.
                    example: 5360b75f-447e-467c-9299-12fd6c92450d
                  name:
                    type: string
                    description: The API's name.
                    example: User Management API
                  summary:
                    type: string
                    description: The API's short summary.
                    example: An API to manager users.
                  createdAt:
                    type: string
                    format: date-time
                    description: The date and time at which the API was created.
                    example: '2023-08-22T14:21:57.000Z'
                  createdBy:
                    type: integer
                    format: int64
                    description: The Postman ID of the user that created the API.
                    example: ********
                  updatedAt:
                    type: string
                    format: date-time
                    description: The date and time at which the API was updated.
                    example: '2023-08-22T14:21:57.000Z'
                  updatedBy:
                    type: integer
                    format: int64
                    description: The Postman ID of the user that updated the API.
                    example: ********
                  description:
                    type: string
                    description: The API's description.
                    example: This API includes operations to add/remove/update users.
              - type: object
                title: API Extended Data Schema
                description: |
                  The API's extended data schema.

                  **Note:**

                  Git-connected APIs will only return the `versions` and `gitInfo` query responses. This is because schema and collection information is stored in the connected Git repository. The `gitInfo` object only lists the repository and folder locations of the files.
                allOf:
                  - type: object
                    title: API Base Data Schema
                    description: The API's base data schema.
                    properties:
                      id:
                        type: string
                        description: The API's ID.
                        example: 5360b75f-447e-467c-9299-12fd6c92450d
                      name:
                        type: string
                        description: The API's name.
                        example: User Management API
                      summary:
                        type: string
                        description: The API's short summary.
                        example: An API to manager users.
                      createdAt:
                        type: string
                        format: date-time
                        description: The date and time at which the API was created.
                        example: '2023-08-22T14:21:57.000Z'
                      createdBy:
                        type: integer
                        format: int64
                        description: The Postman ID of the user that created the API.
                        example: ********
                      updatedAt:
                        type: string
                        format: date-time
                        description: The date and time at which the API was updated.
                        example: '2023-08-22T14:21:57.000Z'
                      updatedBy:
                        type: integer
                        format: int64
                        description: The Postman ID of the user that updated the API.
                        example: ********
                      description:
                        type: string
                        description: The API's description.
                        example: This API includes operations to add/remove/update users.
                  - type: object
                    title: API Details
                    description: Detailed information about the API.
                    properties:
                      gitInfo:
                        type: object
                        title: Git Repo Data Schema
                        description: Information about the API's Git repository integration.
                        properties:
                          domain:
                            type: string
                            nullable: true
                            description: The domain at which the Git repository is hosted.
                            example: https://git.example.com
                          repository:
                            type: string
                            description: The repository's name.
                            example: test-api-repo
                          organization:
                            type: string
                            description: The organization that owns the repository.
                            example: test-org
                          schemaFolder:
                            type: string
                            description: The API definition's repository folder location.
                            example: test-api-schema
                          collectionFolder:
                            type: string
                            description: The API definition's collection repository folder location.
                            example: test-api-collection
                      schemas:
                        type: array
                        description: The API's schemas.
                        items:
                          type: object
                          description: Information about the schema.
                          properties:
                            id:
                              type: string
                              description: The schema's ID.
                              example: 5381f010-c4c1-11ed-afa1-0242ac120002
                      versions:
                        type: array
                        description: The API's versions.
                        items:
                          type: object
                          description: Information about the version.
                          properties:
                            id:
                              type: string
                              description: The version's ID.
                              example: 12ece9e1-2abf-4edc-8e34-de66e74114d2
                            name:
                              type: string
                              description: The version's name.
                              example: v1
                      collections:
                        type: array
                        description: The API's collections.
                        items:
                          type: object
                          description: Information about the collection.
                          properties:
                            id:
                              type: string
                              description: The collection's ID.
                              example: ********-61867bcc-c4c1-11ed-afa1-0242ac120002
          example:
            createdBy: ********
            name: Test API
            updatedBy: ********
            updatedAt: '2022-06-09T19:50:49.000Z'
            description: This is a test API.
            id: 5360b75f-447e-467c-9299-12fd6c92450d
            collections:
              - id: 16bb367e-fafb-4ef3-933b-ee3d971866fb
            gitInfo: {}
            schemas:
              - id: ae2b9ab2-28f2-401d-912f-eca09a78e98b
            versions:
              - id: 18ccb2dc-1746-4945-ba76-8152b37cr123
                name: v1.0.0
            createdAt: '2022-06-09T14:48:45.000Z'
            summary: Test API.
    v10HeaderMissing:
      description: Missing v10 Accept Header
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/v10HeaderMissing'
          examples:
            Missing v10 Accept Header:
              $ref: '#/components/examples/v10HeaderMissing'
    updateApi:
      description: Successful Response
      content:
        application/json:
          schema:
            type: object
            description: Information about the API.
            required:
              - name
            properties:
              id:
                type: string
                description: The API's ID.
                example: 5360b75f-447e-467c-9299-12fd6c92450d
              name:
                type: string
                description: The API's name.
                example: Test API A
              summary:
                type: string
                description: The API's summary.
                example: Test API A Schema
              createdAt:
                type: string
                format: date-time
                description: The date and time at which the API was created.
                example: '2022-06-29T20:46:58.000Z'
              createdBy:
                type: string
                description: The user ID of the user that created the API.
                example: '********'
              updatedAt:
                type: string
                format: date-time
                description: The date and time at which the API was last updated.
                example: '2022-06-29T20:46:58.000Z'
              updatedBy:
                type: string
                description: The user ID of the user that updated the API.
                example: '********'
              description:
                type: string
                description: The API's description. This supports Markdown formatting.
                example: This is Test API A.
          example:
            createdAt: '2022-06-29T20:46:58.000Z'
            updatedAt: '2022-06-29T20:46:58.000Z'
            id: 5360b75f-447e-467c-9299-12fd6c92450d
            name: Test API A
            summary: Test API A Schema
            description: This is Test API A.
            createdBy: '********'
            updatedBy: '********'
    api403ErrorAndFeatureUnavailable:
      description: Forbidden
      content:
        application/json:
          schema:
            anyOf:
              - $ref: '#/components/schemas/api403ErrorForbidden'
              - $ref: '#/components/schemas/featureUnavailable403Error'
          examples:
            Forbidden:
              $ref: '#/components/examples/api403ErrorForbidden'
            Feature Unavailable:
              $ref: '#/components/examples/featureUnavailable403Error'
    addApiCollection:
      description: Created
      content:
        application/json:
          schema:
            type: object
            properties:
              id:
                type: string
                description: The collection's ID.
                example: ********-61867bcc-c4c1-11ed-afa1-0242ac120002
          example:
            id: ********-61867bcc-c4c1-11ed-afa1-0242ac120002
    getApiCollection:
      description: Successful Response
      content:
        application/json:
          schema:
            type: object
            additionalProperties: true
            properties:
              info:
                type: object
                additionalProperties: true
                description: Information about the collection.
                properties:
                  name:
                    type: string
                    description: The collection's name.
                    example: Test Collection
                  schema:
                    type: string
                    description: The collection's JSON schema version.
                    enum:
                      - https://schema.getpostman.com/json/collection/v2.1.0/collection.json
                    example: https://schema.getpostman.com/json/collection/v2.1.0/collection.json
                  updatedAt:
                    type: string
                    format: date-time
                    description: The date and time at which the collection was last updated.
                    example: '2023-06-16T20:21:13.000Z'
                  _postman_id:
                    type: string
                    description: The collection's ID.
                    example: 12ece9e1-2abf-4edc-8e34-de66e74114d2
                  description:
                    type: string
                    description: The collection's description.
                    example: This is a test collection that makes a tiny request to Postman Echo service to get the list of request headers sent by a HTTP client.
              item:
                type: array
                items:
                  type: object
                  additionalProperties: true
                  description: Information about the collection's contents. For a complete list of values, refer to the [Postman Collection Format documentation](https://schema.postman.com/collection/json/v2.1.0/draft-07/docs/index.html).
                  properties:
                    id:
                      type: string
                      description: The collection item's ID.
                      example: 82ee981b-e19f-962a-401e-ea34ebfb4848
                    name:
                      type: string
                      description: The collection item's human-readable identifier.
                      example: Test GET Response
                    event:
                      type: array
                      description: The collection's event information. For a complete list of values, refer to the **Event List** entry in the [Postman Collection Format documentation](https://schema.postman.com/collection/json/v2.1.0/draft-07/docs/index.html).
                      items:
                        type: object
                    request:
                      type: object
                      additionalProperties: true
                      description: The collection's request information. For a complete list of values, refer to the **Request** entry in the [Postman Collection Format documentation](https://schema.postman.com/collection/json/v2.1.0/draft-07/docs/index.html).
                    response:
                      type: array
                      description: The collection's response information. For a complete list of values, refer to the **Response** entry in the [Postman Collection Format documentation](https://schema.postman.com/collection/json/v2.1.0/draft-07/docs/index.html).
                      items:
                        type: object
          example:
            item:
              - protocolProfileBehavior:
                  disableBodyPruning: true
                response:
                  - originalRequest:
                      url:
                        host:
                          - https://api.getpostman.com
                        raw: https://api.getpostman.com/spacecrafts/:spacecraftId
                        variable:
                          - description: (Required) The unique identifier of the spacecraft
                            key: spacecraftId
                            value: <string>
                        path:
                          - spacecrafts
                          - ':spacecraftId'
                      method: GET
                      header:
                        - description: 'Added as a part of security scheme: apikey'
                          key: X-Api-Key
                          value: <API Key>
                    code: 200
                    _postman_previewlanguage: json
                    responseTime: null
                    header:
                      - key: Content-Type
                        value: application/json
                    name: The spacecraft corresponding to the provided `spacecraftId`
                    _postman_previewtype: html
                    body: |-
                      {
                        "id": "<string>",
                        "name": "commodo",
                        "type": "capsule",
                        "description": "pariatur dolo"
                      }
                    cookie: []
                    status: OK
                    id: 54467f6e-71d7-43d5-acc0-48f948e38528
                  - originalRequest:
                      url:
                        host:
                          - https://api.getpostman.com
                        raw: https://api.getpostman.com/spacecrafts/:spacecraftId
                        variable:
                          - description: (Required) The unique identifier of the spacecraft
                            key: spacecraftId
                            value: <string>
                        path:
                          - spacecrafts
                          - ':spacecraftId'
                      method: GET
                      header:
                        - description: 'Added as a part of security scheme: apikey'
                          key: X-Api-Key
                          value: <API Key>
                    code: 404
                    _postman_previewlanguage: json
                    responseTime: null
                    header:
                      - key: Content-Type
                        value: application/json
                    name: No spacecraft found for the provided `spacecraftId`
                    _postman_previewtype: html
                    body: |-
                      {
                        "message": "dolore Excepteur"
                      }
                    cookie: []
                    status: Not Found
                    id: 1231609a-7a3d-444d-aa0c-579703e618f4
                  - originalRequest:
                      url:
                        host:
                          - https://api.getpostman.com
                        raw: https://api.getpostman.com/spacecrafts/:spacecraftId
                        variable:
                          - description: (Required) The unique identifier of the spacecraft
                            key: spacecraftId
                            value: <string>
                        path:
                          - spacecrafts
                          - ':spacecraftId'
                      method: GET
                      header:
                        - description: 'Added as a part of security scheme: apikey'
                          key: X-Api-Key
                          value: <API Key>
                    code: 500
                    _postman_previewlanguage: json
                    responseTime: null
                    header:
                      - key: Content-Type
                        value: application/json
                    name: Unexpected error
                    _postman_previewtype: html
                    body: |-
                      {
                        "message": "dolore Excepteur"
                      }
                    cookie: []
                    status: Internal Server Error
                    id: db674465-8cec-4208-9724-42278ca9b83c
                id: d3779255-5293-4e58-9b65-8954936e1dca
                request:
                  url:
                    host:
                      - https://api.getpostman.com
                    raw: https://api.getpostman.com/spacecrafts/:spacecraftId
                    variable:
                      - description: (Required) The unique identifier of the spacecraft
                        key: spacecraftId
                        value: <string>
                    path:
                      - spacecrafts
                      - ':spacecraftId'
                  method: GET
                  header:
                    - key: Accept
                      value: application/json
                name: Read a spacecraft
            auth:
              apikey:
                - key: key
                  value: X-Api-Key
                - key: value
                  value: '{{apiKey}}'
                - key: in
                  value: header
              type: apikey
            info:
              description: Buy or rent spacecrafts
              _postman_id: e726de58-f1b3-4edd-a8a7-2579dc799d39
              schema: https://schema.getpostman.com/json/collection/v2.1.0/collection.json
              name: Generated
            variable:
              - value: /
                type: string
                id: 526a20ba-acfb-4549-a841-8edf2d24b929
                key: baseUrl
    apiCollection404ErrorNotFound:
      description: API Collection Not Found
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/apiCollection404ErrorNotFound'
          examples:
            API Collection Not Found:
              $ref: '#/components/examples/apiCollection404ErrorNotFound'
    gitLinkedApi422Error:
      description: Git-Linked API Errror
      content:
        application/problem+json:
          schema:
            type: object
            properties:
              type:
                type: string
                description: The error type.
                example: actionNotAllowedError
              title:
                type: string
                description: A short summary of the problem.
                example: Action not allowed
              detail:
                type: string
                description: Details about the error.
                example: This action is not allowed for Git linked APIs
          example:
            type: actionNotAllowedError
            title: Action not allowed
            detail: This action is not allowed for Git linked APIs
    syncCollectionWithSchema:
      description: Accepted
      content:
        application/json:
          schema:
            type: object
            properties:
              taskId:
                type: string
                example: 66ae9950-0869-4e65-96b0-1e0e47e771af
                description: The created task ID. You can use this ID to track the status of syncing an API collection with an API schema.
          example:
            taskId: 66ae9950-0869-4e65-96b0-1e0e47e771af
      headers:
        Location:
          $ref: '#/components/headers/Location'
    apiSchema404ErrorNotFound:
      description: Schema Not Found
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/apiSchema404ErrorNotFound'
          examples:
            Schema Not Found:
              $ref: '#/components/examples/apiSchema404ErrorNotFound'
    apiSchema422ErrorActionNotAllowed:
      description: Unprocessable Entity (WebDAV) (RFC 4918)
      content:
        application/problem+json:
          schema:
            type: object
            properties:
              type:
                type: string
                description: The error type.
                example: actionNotAllowedError
              title:
                type: string
                description: A short summary of the problem.
                example: Action not allowed
              detail:
                type: string
                description: Information about the error.
                example: This action is not allowed for git linked APIs
          example:
            type: actionNotAllowedError
            title: Action not allowed
            detail: This action is not allowed for git linked APIs
    comment403ErrorAndFeatureUnavailable:
      description: Forbidden
      content:
        application/json:
          schema:
            anyOf:
              - $ref: '#/components/schemas/comment403Error'
              - $ref: '#/components/schemas/featureUnavailable403Error'
          examples:
            Forbidden:
              $ref: '#/components/examples/comment403Error'
            Feature Unavailable:
              $ref: '#/components/examples/featureUnavailable403Error'
    createApiSchema:
      description: Created
      content:
        application/json:
          schema:
            type: object
            description: Information about the created API schema.
            properties:
              id:
                type: string
                description: The schema's ID.
                example: b4fc1bdc-6587-4f9b-95c9-f768146089b4
              type:
                type: string
                description: The schema's type.
                enum:
                  - proto:2
                  - proto:3
                  - graphql
                  - openapi:3_1
                  - openapi:3
                  - openapi:2
                  - openapi:1
                  - raml:1
                  - raml:0_8
                  - wsdl:2
                  - wsdl:1
                  - asyncapi:2
                example: openapi:3
              files:
                type: array
                description: The list of the schema's files.
                items:
                  type: object
                  title: Schema File Base Data
                  description: Information about the schema file.
                  properties:
                    id:
                      type: string
                      description: The schema file's ID.
                      example: b4fc1bdc-6587-4f9b-95c9-f768146089b4
                    name:
                      type: string
                      description: The schema file's name.
                      example: index.json
                    path:
                      type: string
                      description: The file system path to the schema file.
                      example: index.json
                    createdAt:
                      type: string
                      format: date-time
                      description: The date and time at which the file was created.
                      example: '2023-03-29T11:37:15Z'
                    root:
                      type: object
                      description: An object that contains root file information.
                      properties:
                        enabled:
                          type: boolean
                          description: If true, the file is tagged as the schema's root file.
                          example: true
                    createdBy:
                      type: string
                      description: The user ID of the user that created the file.
                      example: '********'
                    updatedAt:
                      type: string
                      format: date-time
                      description: The date and time at which the file was last updated.
                      example: '2023-03-29T11:37:15Z'
                    updatedBy:
                      type: string
                      description: The user ID of the user that last updated the file.
                      example: '********'
              createdAt:
                type: string
                format: date-time
                description: The date and time at which the schema was created.
                example: '2023-03-29T11:37:15Z'
              createdBy:
                type: string
                description: The user ID of the user that created the schema.
                example: '********'
              updatedAt:
                type: string
                format: date-time
                description: The date and time at which the schema was last updated.
                example: '2023-03-29T11:37:15Z'
              updatedBy:
                type: string
                description: The user ID of the user that updated the schema.
                example: '********'
          example:
            type: openapi:3
            files:
              - createdBy: '********'
                path: index.json
                updatedBy: '********'
                updatedAt: '2024-07-18T13:17:30.000Z'
                root:
                  enabled: true
                id: b4fc1bdc-6587-4f9b-95c9-f768146089b4
                name: index.json
                createdAt: '2024-07-18T13:17:30.000Z'
            updatedAt: '2024-07-18T13:17:30.000Z'
            createdAt: '2024-07-18T13:17:30.000Z'
            createdBy: '********'
            updatedBy: '********'
            id: b4fc1bdc-6587-4f9b-95c9-f768146089b4
    api404ErrorInstanceNotFound:
      description: API Not Found
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/api404ErrorInstanceNotFound'
          examples:
            API Not Found:
              $ref: '#/components/examples/api404ErrorInstanceNotFound'
    getApiSchema:
      description: Successful Response
      content:
        application/json:
          schema:
            anyOf:
              - type: object
                title: Get Schema
                description: Information about the schema.
                properties:
                  id:
                    type: string
                    description: The schema's ID.
                    example: ae2b9ab2-28f2-401d-912f-eca09a78e98b
                  type:
                    type: string
                    description: The schema's type.
                    example: openapi:3
                  files:
                    type: object
                    description: Information about the schema's files. The response is paginated and limited to one page.
                    properties:
                      data:
                        type: array
                        description: A list of the schema files.
                        items:
                          title: Schema File Base Data
                          description: Information about the schema file.
                          type: object
                          properties:
                            id:
                              type: string
                              description: The schema file's ID.
                              example: cf98c187-17c1-455f-afbf-d4be51f12770
                            name:
                              type: string
                              description: The schema file's name.
                              example: s1.json
                            path:
                              type: string
                              description: The file system path to the schema file.
                              example: dir/s1.json
                            createdAt:
                              type: string
                              format: date-time
                              description: The date and time at which the file was created.
                              example: '2023-03-16T18:38:56.000Z'
                            createdBy:
                              type: string
                              description: The user ID of the user that created the file.
                              example: '5000842'
                            updatedAt:
                              type: string
                              format: date-time
                              description: The date and time at which the file was last updated.
                              example: '2023-03-16T19:11:24.000Z'
                            updatedBy:
                              type: string
                              description: The user ID of the user that last updated the file.
                              example: '5000842'
                      meta:
                        type: object
                        properties:
                          nextPath:
                            type: string
                            description: The URL path to the next file.
                            example: /apis/1fdbff7c-036b-4f8a-91bc-17bf3ae74fd2/schemas/cf98c187-17c1-455f-afbf-d4be51f12770/files?cursor=eyJzY2hlbWUiOiJwYXRoX2FzYyIsImRpcmVjdGlvblR5cGUiOiJuZXh0IiwicGl2b3QiOiJwYXRoIiwidmFsdWUiOiJkaXIvczEuanNvbiJ9
                  createdAt:
                    type: string
                    format: date-time
                    description: The date and time at which the schema was created.
                    example: '2022-03-29T11:37:15Z'
                  createdBy:
                    type: string
                    description: The user ID of the user that created the schema.
                    example: '********'
                  updatedAt:
                    type: string
                    format: date-time
                    description: The date and time at which the schema was last updated.
                    example: '2022-03-29T11:37:15Z'
                  updatedBy:
                    type: string
                    description: The user ID of the user that last updated the schema.
                    example: '********'
              - type: object
                title: Get Bundled Schema
                description: Information about the schema.
                properties:
                  id:
                    type: string
                    description: The schema's ID.
                    example: ae2b9ab2-28f2-401d-912f-eca09a78e98b
                  type:
                    type: string
                    description: The schema's type.
                    example: openapi:3
                  createdBy:
                    type: string
                    description: The user ID of the user that created the schema.
                    example: '********'
                  updatedBy:
                    type: string
                    description: The user ID of the user that last updated the schema.
                    example: '********'
                  createdAt:
                    type: string
                    format: date-time
                    example: '2022-03-29T11:37:15Z'
                    description: The date and time at which the schema was created.
                  updatedAt:
                    type: string
                    format: date-time
                    description: The date and time at which the schema was last updated.
                    example: '2022-03-29T11:37:15Z'
                  content:
                    type: string
                    description: The schema file, in a bundled format.
                    example: |-
                      openapi: '3.0.0'
                      info:
                        version: '1.0.0'
                        title: 'Sample API'
                        description: Buy or rent spacecrafts

                      paths:
                        /spacecrafts/{spacecraftId}:
                          parameters:
                            - name: spacecraftId
                              description: The unique identifier of the spacecraft
                              in: path
                              required: true
                              schema:
                                $ref: '#/components/schemas/SpacecraftId'
                          get:
                            summary: Read a spacecraft
                            responses:
                              '200':
                                description: The spacecraft corresponding to the provided `spacecraftId`
                                content:
                                  application/json:
                                    schema:
                                      $ref: '#/components/schemas/Spacecraft'
                              404:
                                description: No spacecraft found for the provided `spacecraftId`
                                content:
                                  application/json:
                                    schema:
                                      $ref: '#/components/schemas/Error'
                              500:
                                description: Unexpected error
                                content:
                                  application/json:
                                    schema:
                                      $ref: '#/components/schemas/Error'
                      components:
                        schemas:
                          SpacecraftId:
                            description: The unique identifier of a spacecraft
                            type: string
                          Spacecraft:
                            type: object
                            required:
                              - id
                              - names
                              - type
                            properties:
                              id:
                                $ref: '#/components/schemas/SpacecraftId'
                              name:
                                type: string
                              type:
                                type: string
                                enum:
                                  - capsule
                                  - probe
                                  - satellite
                                  - spaceplane
                                  - station
                              description:
                                type: string
                          Error:
                            type: object
                            required:
                              - message
                            properties:
                              message:
                                description: A human readable error message
                                type: string
                        securitySchemes:
                          ApiKey:
                            type: apiKey
                            in: header
                            name: X-Api-Key
                      security:
                        - ApiKey: []
          example:
            createdAt: '2022-03-29T11:37:15Z'
            updatedAt: '2022-03-29T11:37:15Z'
            files:
              meta:
                nextPath: /apis/1fdbff7c-036b-4f8a-91bc-17bf3ae74fd2/schemas/cf98c187-17c1-455f-afbf-d4be51f12770/files?cursor=eyJzY2hlbWUiOiJwYXRoX2FzYyIsImRpcmVjdGlvblR5cGUiOiJuZXh0IiwicGl2b3QiOiJwYXRoIiwidmFsdWUiOiJkaXIvczEuanNvbiJ9
              data:
                - createdBy: '********'
                  path: dir/s1.json
                  updatedBy: '********'
                  updatedAt: '2023-03-16T19:11:24.000Z'
                  createdAt: '2023-03-16T18:38:56.000Z'
                  id: cf98c187-17c1-455f-afbf-d4be51f12770
                  name: s1.json
    getApiSchemaFiles:
      description: Successful Response
      content:
        application/json:
          schema:
            type: object
            description: Information about the schema files and its meta information.
            properties:
              meta:
                type: object
                description: The schema's non-standard meta information.
                properties:
                  nextCursor:
                    type: string
                    description: The pointer to the next record in the set of paginated results.
                    example: eyJzY2hlbWUiOiJwYXRoX2FzYyIsImRpcmVjdGlvblR5cGUiOiJuZXh0IiwicGl2b3QiOiJwYXRoIiwidmFsdWUiOiJOZXdQZXQuanNvbiJ9
              files:
                type: array
                description: The schema's files.
                items:
                  type: object
                  title: Schema File Base Data
                  description: Information about the schema file.
                  properties:
                    id:
                      type: string
                      description: The schema file's ID.
                      example: e8a015e0-f472-4bb3-a523-57ce7c4583ed
                    name:
                      type: string
                      description: The schema file's name.
                      example: error.yaml
                    path:
                      type: string
                      description: The file system path to the schema file.
                      example: data-model/error.yaml
                    createdAt:
                      type: string
                      format: date-time
                      description: The date and time at which the file was created.
                      example: '2022-03-29T11:37:15Z'
                    createdBy:
                      type: integer
                      format: int64
                      description: The user Id of the user that created the file.
                      example: ********
                    updatedAt:
                      type: string
                      format: date-time
                      description: The date and time at which the file was last updated.
                      example: '2022-03-29T11:37:15Z'
                    updatedBy:
                      type: integer
                      format: int64
                      description: The user ID of the user that last updated the file.
                      example: ********
          example:
            meta:
              nextCursor: eyJzY2hlbWUiOiJwYXRoX2FzYyIsImRpcmVjdGlvblR5cGUiOiJuZXh0IiwicGl2b3QiOiJwYXRoIiwidmFsdWUiOiJOZXdQZXQuanNvbiJ9
            files:
              - id: e8a015e0-f472-4bb3-a523-57ce7c4583ed
                path: data-model/error.yaml
                name: error.yaml
                createdAt: '2022-03-29T11:37:15Z'
                updatedAt: '2022-03-29T11:37:15Z'
                createdBy: ********
                updatedBy: ********
              - id: e8a015e0-f472-4bb3-a523-57ce7c4583ef
                path: data-model/error2.yaml
                name: error2.yaml
                createdAt: '2022-03-29T11:37:15Z'
                updatedAt: '2022-03-29T11:37:15Z'
                createdBy: ********
                updatedBy: ********
    getApiSchemaFileContents:
      description: Successful Response
      content:
        application/json:
          schema:
            type: object
            title: Schema File Contents
            description: Information about the schema file.
            properties:
              id:
                type: string
                description: The schema file's ID.
                example: 0784657a-668d-4530-85c8-468becdb06fd
              name:
                type: string
                description: The schema file's name.
                example: index.json
              path:
                type: string
                description: The file system path to the schema file.
                example: common/Test.json
              content:
                type: string
                description: The schema file's stringified contents.
                example: |-
                  openapi: '3.0.0'
                  info:
                    version: '1.0.0'
                    title: 'Sample API'
                    description: Buy or rent spacecrafts

                  paths:
                    /spacecrafts/{spacecraftId}:
                      parameters:
                        - name: spacecraftId
                          description: The unique identifier of the spacecraft
                          in: path
                          required: true
                          schema:
                            $ref: '#/components/schemas/SpacecraftId'
                      get:
                        summary: Read a spacecraft
                        responses:
                          '200':
                            description: The spacecraft corresponding to the provided `spacecraftId`
                            content:
                              application/json:
                                schema:
                                  $ref: '#/components/schemas/Spacecraft'
                          404:
                            description: No spacecraft found for the provided `spacecraftId`
                            content:
                              application/json:
                                schema:
                                  $ref: '#/components/schemas/Error'
                          500:
                            description: Unexpected error
                            content:
                              application/json:
                                schema:
                                  $ref: '#/components/schemas/Error'
                  components:
                    schemas:
                      SpacecraftId:
                        description: The unique identifier of a spacecraft
                        type: string
                      Spacecraft:
                        type: object
                        required:
                          - id
                          - names
                          - type
                        properties:
                          id:
                            $ref: '#/components/schemas/SpacecraftId'
                          name:
                            type: string
                          type:
                            type: string
                            enum:
                              - capsule
                              - probe
                              - satellite
                              - spaceplane
                              - station
                          description:
                            type: string
                      Error:
                        type: object
                        required:
                          - message
                        properties:
                          message:
                            description: A human readable error message
                            type: string
                    securitySchemes:
                      ApiKey:
                        type: apiKey
                        in: header
                        name: X-Api-Key
                  security:
                    - ApiKey: []
              createdAt:
                type: string
                format: date-time
                description: The date and time at which the file was created.
                example: '2023-03-15T13:27:45.000Z'
              createdBy:
                type: string
                description: The user Id of the user that created the file.
                example: '********'
              updatedAt:
                type: string
                format: date-time
                description: The date and time at which the file was last updated.
                example: '2023-03-15T13:27:45.000Z'
              updatedBy:
                type: string
                description: The user ID of the user that last updated the file.
                example: '********'
          example:
            id: 0784657a-668d-4530-85c8-468becdb06fd
            name: Test.json
            path: common/Test.json
            content: |-
              openapi: '3.0.0'
              info:
                version: '1.0.0'
                title: 'Sample API'
                description: Buy or rent spacecrafts

              paths:
                /spacecrafts/{spacecraftId}:
                  parameters:
                    - name: spacecraftId
                      description: The unique identifier of the spacecraft
                      in: path
                      required: true
                      schema:
                        $ref: '#/components/schemas/SpacecraftId'
                  get:
                    summary: Read a spacecraft
                    responses:
                      '200':
                        description: The spacecraft corresponding to the provided `spacecraftId`
                        content:
                          application/json:
                            schema:
                              $ref: '#/components/schemas/Spacecraft'
                      404:
                        description: No spacecraft found for the provided `spacecraftId`
                        content:
                          application/json:
                            schema:
                              $ref: '#/components/schemas/Error'
                      500:
                        description: Unexpected error
                        content:
                          application/json:
                            schema:
                              $ref: '#/components/schemas/Error'
              components:
                schemas:
                  SpacecraftId:
                    description: The unique identifier of a spacecraft
                    type: string
                  Spacecraft:
                    type: object
                    required:
                      - id
                      - names
                      - type
                    properties:
                      id:
                        $ref: '#/components/schemas/SpacecraftId'
                      name:
                        type: string
                      type:
                        type: string
                        enum:
                          - capsule
                          - probe
                          - satellite
                          - spaceplane
                          - station
                      description:
                        type: string
                  Error:
                    type: object
                    required:
                      - message
                    properties:
                      message:
                        description: A human readable error message
                        type: string
                securitySchemes:
                  ApiKey:
                    type: apiKey
                    in: header
                    name: X-Api-Key
              security:
                - ApiKey: []
            createdAt: '2023-03-15T13:27:45.000Z'
            createdBy: '********'
            updatedAt: '2023-03-15T13:27:45.000Z'
            updatedBy: '********'
    createUpdateApiSchemaFile:
      description: Successful Response
      content:
        application/json:
          schema:
            type: object
            title: Schema File Base Data
            description: Information about the schema file.
            properties:
              createdBy:
                type: string
                description: The user ID of the user that created the file.
                example: '********'
              createdAt:
                type: string
                format: date-time
                description: The date and time at which the file was created.
                example: '2024-07-18T13:48:28.000Z'
              root:
                type: object
                description: Information about the schema's root file.
                properties:
                  enabled:
                    type: boolean
                    description: If true, the file is the schema's the root file.
                    example: true
              name:
                type: string
                description: The schema file's name.
                example: index.json
              path:
                type: string
                description: The file system path to the schema file.
                example: index.json
              updatedBy:
                type: string
                description: The user ID of the user that last updated the file.
                example: '********'
              id:
                type: string
                description: The schema file's ID.
                example: 2fdc8ea1-d02e-4e50-989e-6fa28f42b995
              updatedAt:
                type: string
                format: date-time
                description: The date and time at which the file was last updated.
                example: '2024-07-18T13:48:28.000Z'
          example:
            createdBy: '********'
            createdAt: '2024-07-18T13:47:39.000Z'
            root:
              enabled: true
            name: index.json
            path: index.json
            updatedBy: '********'
            id: 2fdc8ea1-d02e-4e50-989e-6fa28f42b995
            updatedAt: '2024-07-18T13:48:28.000Z'
    getStatusOfAnAsyncTask:
      description: Successful Response
      content:
        application/json:
          schema:
            type: object
            properties:
              id:
                type: string
                description: The task's ID.
                example: 10ca9f5a-c4c4-11ed-afa1-0242ac120002
              meta:
                type: object
                description: The response's non-standard meta information.
                properties:
                  url:
                    type: string
                    format: url
                    description: The endpoint URL that created the task.
                    example: https://api.getpostman.com/apis/5360b75f-447e-467c-9299-12fd6c92450d/collections/e726de58-f1b3-4edd-a8a7-2579dc799d39/sync
                  model:
                    type: string
                    description: The model for which the task is performing the operation.
                    enum:
                      - collection
                      - api-version
                    example: collection
                  action:
                    type: string
                    description: The task's action.
                    enum:
                      - update
                      - create
                    example: update
              status:
                description: The task's current status.
                enum:
                  - pending
                  - failed
                  - completed
                type: string
                example: completed
              details:
                anyOf:
                  - type: object
                    title: Task Resources
                    description: Information about the task's resources.
                    properties:
                      resources:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: string
                              description: The ID of the assigned resource.
                              example: collectionID
                            url:
                              type: string
                              description: The task's assigned resource URL.
                              example: https://api.getpostman.com/apis/5360b75f-447e-467c-9299-12fd6c92450d/collections/e726de58-f1b3-4edd-a8a7-2579dc799d39
                  - type: object
                    title: Task Error Information
                    description: Information about the error that occurred during the task's processing.
                    properties:
                      error:
                        type: object
                        properties:
                          message:
                            type: string
                            description: The task's error message.
                            example: The API contains invalid schema
              createdAt:
                type: string
                format: date-time
                description: The date and time at which the task was created.
                example: '2023-08-22T14:21:57.000Z'
              updatedAt:
                type: string
                format: date-time
                description: The date and time at which the task was last updated.
                example: '2023-08-22T14:21:57.000Z'
          example:
            id: 66ae9950-0869-4e65-96b0-1e0e47e771af
            meta:
              url: https://api.getpostman.com/apis/5360b75f-447e-467c-9299-12fd6c92450d/collections/e726de58-f1b3-4edd-a8a7-2579dc799d39/sync
              model: collection
              action: update
            status: failed
            details:
              error:
                message: The API contains invalid schema
            createdAt: '2019-02-12 19:34:49'
            updatedAt: '2023-02-12 19:34:49'
    taskNotFound:
      description: Task Not Found
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/taskNotFound'
          examples:
            Task Not Found:
              $ref: '#/components/examples/taskNotFound'
    getApiVersions:
      description: Successful Response
      content:
        application/json:
          schema:
            type: object
            description: Information about the API's versions.
            properties:
              meta:
                type: object
                description: The response's meta information for paginated results.
                properties:
                  limit:
                    type: integer
                    description: The maximum number of records in the paginated response.
                    example: 100
                  total:
                    type: integer
                    description: The number of records that match the defined criteria.
                    example: 1000
                  nextCursor:
                    type: string
                    format: base64
                    description: The pagination cursor that points to the next record in the results set.
                    example: VGh1IE1hciAxNiAyMDIzIDE3OjIxOjUzIEdNVCswMDAwIChDb29yZGluYXRlZCBVbml2ZXJzYWwgVGltZSk=
              versions:
                type: array
                items:
                  type: object
                  title: API Version Data Schema
                  description: Information about the API version.
                  properties:
                    id:
                      type: string
                      description: The version's ID.
                      example: 12ece9e1-2abf-4edc-8e34-de66e74114d2
                    name:
                      type: string
                      description: The version's name.
                      example: Release 1.0
                    createdAt:
                      type: string
                      format: date-time
                      description: The date and time at which the version was created.
                      example: '2023-06-09T14:48:45.000Z'
                    updatedAt:
                      type: string
                      format: date-time
                      description: The date and time at which the version was last updated.
                      example: '2023-06-09T14:48:45.000Z'
                    releaseNotes:
                      type: string
                      description: The version's release notes.
                      example: This is the first release.
          example:
            versions:
              - createdAt: '2022-06-09T14:48:45.000Z'
                updatedAt: '2022-06-09T19:50:49.000Z'
                id: 07d940bf-40fc-4acd-a11e-be6769894af0
                name: Release 2.0
                releaseNotes: This is the second release of the Test API.
              - createdAt: '2022-06-09T14:48:45.000Z'
                updatedAt: '2022-06-09T19:50:49.000Z'
                id: 3563baaa-07a2-46ed-9fd2-0e8a3c5f7ec2
                name: Release 1.0
                releaseNotes: This is the first release of the Test API. For more information, read the documentation.
            meta:
              nextCursor: VGh1IE1hciAxNiAyMDIzIDE3OjIxOjUzIEdNVCswMDAwIChDb29yZGluYXRlZCBVbml2ZXJzYWwgVGltZSk=
              limit: 2
              total: 1000
    apiVersions404Response:
      description: Not Found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/api404ErrorNotFound'
          examples:
            API Not Found:
              $ref: '#/components/examples/api404ErrorNotFound'
    createApiVersion:
      description: Accepted
      content:
        application/json:
          schema:
            type: object
            properties:
              id:
                type: string
                description: The version's ID.
                example: 12ece9e1-2abf-4edc-8e34-de66e74114d2
              createdAt:
                type: string
                format: date-time
                description: The date and time at which the version was created.
                example: '2023-06-09T14:48:45.000Z'
              updatedAt:
                type: string
                format: date-time
                description: The date and time at which the version was last updated.
                example: '2023-06-09T19:50:49.000Z'
              name:
                type: string
                description: The version's name.
                example: v1
              releaseNotes:
                type: string
                description: Information about the API version release. For example, changelog notes.
                example: This is the first release.
          example:
            createdAt: '2023-06-09T14:48:45.000Z'
            updatedAt: '2023-06-09T19:50:49.000Z'
            id: 12ece9e1-2abf-4edc-8e34-de66e74114d2
            name: v1
            releaseNotes: This is the first release.
      headers:
        Location:
          $ref: '#/components/headers/Location'
    apiVersion422ErrorStateInconsistent:
      description: API State Inconsistent
      content:
        application/problem+json:
          schema:
            type: object
            properties:
              type:
                type: string
                description: The error type.
                example: APIStateInconsistent
              title:
                type: string
                description: A short summary of the problem.
                example: API state seems to be inconsistent.
              detail:
                type: string
                description: Details about the error.
                example: Please perform a Git push from the Postman app to sync state between app and repository.
          example:
            type: APIStateInconsistent
            title: API state seems to be inconsistent.
            detail: Please perform a Git push from the Postman app to sync state between app and repository.
    getApiVersion:
      description: Successful Response
      content:
        application/json:
          schema:
            type: object
            title: API Version Data Schema
            properties:
              version:
                type: object
                description: Information about the API version.
                properties:
                  id:
                    type: string
                    description: The version's ID.
                    example: 12ece9e1-2abf-4edc-8e34-de66e74114d2
                  name:
                    type: string
                    description: The version's name.
                    example: Release 1.0
                  createdAt:
                    type: string
                    format: date-time
                    description: The date and time at which the version was created.
                    example: '2023-06-09T14:48:45.000Z'
                  updatedAt:
                    type: string
                    format: date-time
                    description: The date and time at which the version was last updated.
                    example: '2023-06-09T14:48:45.000Z'
                  releaseNotes:
                    type: string
                    description: The version's release notes.
                    example: This is the first release.
                  schemas:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: string
                          description: The schema's ID.
                          example: 13a441a3-6109-4f78-86eb-e0144d0ff275
                        type:
                          type: string
                          description: The schema type.
                          example: openapi:3
                  collections:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: string
                          description: The collection's ID.
                          example: aeedb353-494b-4424-82cf-358aece2bd1c
                        type:
                          type: string
                          description: The collection's name.
                          example: collection1
          example:
            version:
              createdAt: '2022-06-09T14:48:45.000Z'
              updatedAt: '2022-06-09T19:50:49.000Z'
              id: 3563baaa-07a2-46ed-9fd2-0e8a3c5f7ec2
              name: Release 1.0
              releaseNotes: This is the first release.
              schemas:
                - id: 13a441a3-6109-4f78-86eb-e0144d0ff275
                  type: openapi:3
              collections:
                - id: aeedb353-494b-4424-82cf-358aece2bd1c
                  name: collection1
    apiVersion404ErrorNotFound:
      description: API Version Not Found
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/apiVersion404ErrorNotFound'
          examples:
            API Version Not Found:
              $ref: '#/components/examples/apiVersion404ErrorNotFound'
    updateApiVersion:
      description: Successful Response
      content:
        application/json:
          schema:
            type: object
            properties:
              id:
                type: string
                description: The version's ID.
                example: 8d7ba74e-2c36-4e36-a263-f3c0c9ae21d2
              name:
                type: string
                description: The version's name.
                example: Release 1.5
              createdAt:
                type: string
                format: date-time
                description: The date and time at which the version was created.
                example: '2022-06-09T14:48:45.000Z'
              updatedAt:
                type: string
                format: date-time
                description: The date and time at which the version was last updated.
                example: '2022-06-09T19:50:49.000Z'
              releaseNotes:
                type: string
                description: The version's release notes.
                example: This is the first public release update.
          example:
            createdAt: '2022-06-09T14:48:45.000Z'
            updatedAt: '2022-06-09T19:50:49.000Z'
            id: 8d7ba74e-2c36-4e36-a263-f3c0c9ae21d2
            name: Release 1.5
            releaseNotes: This is the first public release update.
    getAuditLogs:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/getAuditLogs'
          examples:
            Successful Response:
              $ref: '#/components/examples/getAuditLogs'
    getCollectionAccessKeys:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/getCollectionAccessKeys'
          example:
            data:
              - id: Njg5OjU3MDQ1NjYtYmQxZDU3NzktMWVkNS00ZDhjLWI0ZmQtZWRhOGY2Mzg1NTY0
                token: PMAT-**********************43BR
                status: ACTIVE
                teamId: 123
                userId: ********
                collectionId: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
                expiresAfter: '2024-06-11T13:21:11.000Z'
                lastUsedAt: ''
                createdAt: '2024-04-12T13:21:11.000Z'
                updatedAt: '2024-04-09T11:00:53.000Z'
              - id: K7yJPzQ18BC7Snm09PXL12RMmnq57hQorFJW8JnCKhQ11JmNQiTlgXnQ1p93jGYN
                token: PMAT-**********************51FZ
                status: ACTIVE
                teamId: 123
                userId: 56781234
                collectionId: 56781234-68e5e7ac-c134-45f4-9770-40e72f3fc474
                expiresAfter: '2024-06-11T13:21:11.000Z'
                lastUsedAt: '2024-04-29T08:24:23.000Z'
                createdAt: '2024-04-22T10:11:00.000Z'
                updatedAt: '2024-04-22T10:11:00.000Z'
            meta:
              nextCursor: b2Zmc2V0PTEwJmxpbWl0PTEw
              prevCursor: ''
    common400ErrorInvalidCursor:
      description: Bad Request
      content:
        application/problem+json:
          schema:
            type: object
            properties:
              type:
                type: string
                format: uri-reference
                description: The [URI reference](https://www.rfc-editor.org/rfc/rfc3986) that identifies the type of problem.
                example: https://api.postman.com/problems/bad-request
              title:
                type: string
                description: A short summary of the problem.
                example: Malformed request
              detail:
                type: string
                description: Information about the error.
                example: Invalid cursor
              status:
                type: integer
                format: http-status-code
                description: The error's HTTP status code.
                example: 400
          example:
            type: https://api.postman.com/problems/bad-request
            title: Malformed request
            detail: Invalid cursor
            status: 400
    common403ErrorForbidden:
      description: Forbidden
      content:
        application/problem+json:
          schema:
            type: object
            properties:
              type:
                type: string
                format: uri-reference
                description: The [URI reference](https://www.rfc-editor.org/rfc/rfc3986) that identifies the type of problem.
                example: https://api.postman.com/problems/forbidden
              title:
                type: string
                description: A short summary of the problem.
                example: Forbidden
              status:
                type: number
                format: http-status-code
                description: The error's HTTP status code.
                example: 403
              detail:
                type: string
                description: Information about the error.
                example: Forbidden
          example:
            type: https://api.postman.com/problems/forbidden
            title: Forbidden
            detail: Forbidden
            status: 403
    common500ErrorSomethingWrong:
      description: Internal Server Error
      content:
        application/problem+json:
          schema:
            type: object
            properties:
              type:
                type: string
                format: uri-reference
                description: The [URI reference](https://www.rfc-editor.org/rfc/rfc3986) that identifies the type of problem.
                example: https://api.postman.com/problems/internal-server-error
              title:
                type: string
                description: A short summary of the problem.
                example: Something went wrong
              detail:
                type: string
                description: An explanation about the problem.
                example: Something went wrong
              status:
                type: integer
                format: http-status-code
                description: The HTTP status code generated by the origin server.
                example: 500
          example:
            type: https://api.postman.com/problems/internal-server-error
            title: Something went wrong
            detail: Something went wrong
            status: 500
    cakNotFound404Error:
      description: Not Found
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/cakNotFound404Error'
          example:
            type: https://api.postman.com/problems/not-found
            title: Not Found
            status: 404
            detail: The key was not found
    asyncMergeCollectionFork:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/asyncMergeCollectionFork'
          examples:
            Successful Response:
              value:
                task:
                  id: pm~vc:merge-workflow~********-4824989b-cd77-4e2f-856c-1ff2a3e9aaca
                  status: in-progress
    collectionForks400ErrorMalformedRequest:
      description: Malformed Request
      content:
        application/json:
          schema:
            type: object
            properties:
              type:
                type: string
                format: uri-reference
                description: The [URI reference](https://www.rfc-editor.org/rfc/rfc3986) that identifies the type of problem.
                example: https://api.postman.com/problems/bad-request
              title:
                type: string
                description: A short summary of the problem.
                example: Malformed request
              detail:
                type: string
                description: Information about the error.
                example: Some validation errors occurred
              status:
                type: number
                format: http-status-code
                description: The error's HTTP status code.
                example: 400
          example:
            type: https://api.postman.com/problems/bad-request
            title: Malformed request
            status: 400
            detail: Some validation errors occurred
    collectionForks403ErrorForbidden:
      description: Forbidden
      content:
        application/json:
          schema:
            type: object
            properties:
              type:
                type: string
                format: uri-reference
                description: The [URI reference](https://www.rfc-editor.org/rfc/rfc3986) that identifies the type of problem.
                example: https://api.postman.com/problems/forbidden
              title:
                type: string
                description: A short summary of the problem.
                example: Forbidden
              detail:
                type: string
                description: Information about the error.
                example: You need Editor role on the destination entity and Viewer role on the source entity to merge changes. Additionally, you may also need Editor role on the source entity to update or delete while merging.
              status:
                type: number
                format: http-status-code
                description: The error's HTTP status code.
                example: 403
          example:
            type: https://api.postman.com/problems/forbidden
            title: Forbidden
            detail: You need Editor role on the destination entity and Viewer role on the source entity to merge changes. Additionally, you may also need Editor role on the source entity to update or delete while merging.
            status: 403
    asyncMergePullCollectionTaskStatus:
      description: Task Status Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/asyncMergePullCollectionTaskStatus'
          examples:
            Success:
              value:
                id: pm~vc:merge-workflow~********-5a027c05-056b-49f5-ac9d-37c29f2d91c3
                status: successful
            In Progress:
              value:
                id: pm~vc:merge-workflow~********-5a027c05-056b-49f5-ac9d-37c29f2d91c3
                status: in-progress
            Failed:
              value:
                id: pm~vc:merge-workflow~********-5a027c05-056b-49f5-ac9d-37c29f2d91c3
                status: failed
                details:
                  error:
                    message: All conflicts have not be resolved
    collectionForks404ErrorTaskNotFound:
      description: Task Not Found
      content:
        application/json:
          schema:
            type: object
            properties:
              type:
                type: string
                format: uri-reference
                description: The [URI reference](https://www.rfc-editor.org/rfc/rfc3986) that identifies the type of problem.
                example: https://api.postman.com/problems/not-found
              title:
                type: string
                description: A short summary of the problem.
                example: Task not found
              detail:
                type: string
                description: Information about the error.
                example: 'Workflow :: pm~vc:merge-workflow~********-5a027c05-056b-49f5-ac9d-37c29f2d91c3 not found'
              status:
                type: number
                format: http-status-code
                description: The error's HTTP status code.
                example: 404
          example:
            type: https://api.postman.com/problems/not-found
            title: Task not found
            detail: 'Workflow :: pm~vc:merge-workflow~********-5a027c05-056b-49f5-ac9d-37c29f2d91c3 not found'
            status: 404
    getCollections:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/getCollections'
          examples:
            Successful Response:
              $ref: '#/components/examples/getCollections'
            Response with Pagination:
              $ref: '#/components/examples/getCollectionsPaginated'
            Invalid Workspace:
              $ref: '#/components/examples/invalidWorkspace'
    createCollection:
      description: Successful Response
      content:
        application/json:
          schema:
            type: object
            properties:
              collection:
                type: object
                properties:
                  id:
                    type: string
                    description: The collection's ID.
                    example: 12ece9e1-2abf-4edc-8e34-de66e74114d2
                  name:
                    type: string
                    description: The collection's name.
                    example: Test Collection
                  uid:
                    type: string
                    format: uid
                    description: The collection's unique ID.
                    example: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
          example:
            collection:
              id: 12ece9e1-2abf-4edc-8e34-de66e74114d2
              name: Test Collection
              uid: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
    collection400ErrorInstanceFound:
      description: Bad Request
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                type: object
                properties:
                  name:
                    type: string
                    description: The error name.
                    example: malformedRequestError
                  message:
                    type: string
                    description: The error message.
                    example: Found 1 errors with the supplied collection.
                  details:
                    type: object
                    description: Information about the error.
                    properties:
                      item:
                        type: string
                        description: The element type.
                        example: collection
                      id:
                        type: string
                        description: The element's ID.
                        example: 12ece9e1-2abf-4edc-8e34-de66e74114d2
          example:
            error:
              name: instanceFoundError
              message: The specified item already exists.
              details:
                item: collection
                id: 12ece9e1-2abf-4edc-8e34-de66e74114d2
    getCollectionsForkedByUser:
      description: Successful Response
      content:
        application/json:
          schema:
            type: object
            properties:
              data:
                type: array
                description: A list of the user's forked collections.
                items:
                  type: object
                  description: Information about the forked collection.
                  properties:
                    forkName:
                      type: string
                      description: The forked collection's label.
                      example: Collection Fork
                    forkId:
                      type: string
                      format: uid
                      description: The forked collection's ID.
                      example: ********-667df1ad-2ee3-7890-b678-7742d82e2e1f
                    sourceId:
                      type: string
                      description: The ID of the forked collection's source collection.
                      example: 87654321-bd46b634-b347-44d4-aa23-8c71ff4ecc4d
                    createdAt:
                      type: string
                      format: date-time
                      description: The date and time at which the fork was created.
                      example: '2023-11-16T09:18:17.000Z'
              meta:
                type: object
                description: The response's meta information for paginated results.
                properties:
                  total:
                    type: number
                    description: The total number of forked collections.
                    example: 2
                  nextCursor:
                    type: string
                    nullable: true
                    format: base64
                    description: The pagination cursor that points to the next record in the results set.
                    example: eyJpZCI6MzMyNzksImZvcmtzQWZ0ZXIiOjIwfQ==
                  inaccessibleFork:
                    type: number
                    description: The total number of forked collections that the user cannot access.
                    example: 0
          example:
            data:
              - forkName: Collection Fork
                forkId: ********-667df1ad-2ee3-7890-b678-7742d82e2e1f
                sourceId: 87654321-bd46b634-b347-44d4-aa23-8c71ff4ecc4d
                createdAt: '2023-11-16T09:18:17.000Z'
              - forkName: Test Fork
                forkId: ********-38j9f1ad-2ee3-7890-dv5h-7742d82e2e1f
                sourceId: 87654321-bd46b634-b347-44d4-aa23-8c71ff4ecc4d
                createdAt: '2023-11-16T09:16:15.000Z'
            meta:
              total: 2
              nextCursor: null
              inaccessibleFork: 0
    fork400ErrorNoUserFound:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/fork400ErrorNoUserFound'
          example:
            collection:
              name: badRequest
              message: No Postman User found for given filters
    createCollectionFork:
      description: Successful Response
      content:
        application/json:
          schema:
            type: object
            properties:
              collection:
                type: object
                description: Information about the forked collection.
                properties:
                  id:
                    type: string
                    description: The forked collection's ID.
                    example: 09547fef-a9a5-4e00-998b-aa563e8db69a
                  name:
                    type: string
                    description: The collection's name.
                    example: Test Collection
                  fork:
                    type: object
                    description: Information about the collection's fork.
                    properties:
                      label:
                        type: string
                        description: The fork's label.
                        example: Test Fork
                      createdAt:
                        type: string
                        format: date-time
                        description: The fork's creation date and time.
                        example: '2022-06-16T19:51:44.069Z'
                      from:
                        type: string
                        format: uid
                        description: The unique ID of fork's source collection.
                        example: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
                  uid:
                    type: string
                    format: uid
                    description: The forked collection's unique ID.
                    example: ********-09547fef-a9a5-4e00-998b-aa563e8db69a
          example:
            collection:
              id: 09547fef-a9a5-4e00-998b-aa563e8db69a
              name: Test Collection
              fork:
                label: Test Fork
                createdAt: '2022-06-16T19:51:44.069Z'
                from: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
              uid: ********-09547fef-a9a5-4e00-998b-aa563e8db69a
    instanceNotFoundCollection:
      description: Instance Not Found
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                type: object
                properties:
                  name:
                    type: string
                    description: The error name.
                    example: instanceNotFoundError
                  message:
                    type: string
                    description: The error message.
                    example: The specified item does not exist.
                  details:
                    type: object
                    description: Information about the error.
                    properties:
                      item:
                        type: string
                        description: The instance item.
                        example: collection
                      id:
                        type: string
                        description: The collection ID.
                        example: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
          example:
            error:
              name: instanceNotFoundError
              message: The specified item does not exist.
              details:
                item: collection
                id: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
    mergeCollectionFork:
      description: Successful Response
      content:
        application/json:
          schema:
            type: object
            properties:
              collection:
                type: object
                properties:
                  id:
                    type: string
                    description: The source collection's ID.
                    example: 12ece9e1-2abf-4edc-8e34-de66e74114d2
                  uid:
                    type: string
                    format: uid
                    description: The source collection's unique ID.
                    example: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
          example:
            collection:
              id: 12ece9e1-2abf-4edc-8e34-de66e74114d2
              uid: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
    common403ErrorForbiddenError:
      description: Forbidden
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                type: object
                properties:
                  name:
                    type: string
                    description: The error name.
                    example: forbiddenError
                  message:
                    type: string
                    description: The error message.
                    example: You do not have enough permissions to perform this action.
          example:
            error:
              name: forbiddenError
              message: You do not have enough permissions to perform this action.
    getCollection:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/getCollection'
          examples:
            Successful Response:
              $ref: '#/components/examples/getCollection'
            Forked Collection Response:
              $ref: '#/components/examples/getCollectionForked'
    collection400ErrorCollectionNotFound:
      description: Collection Not Found
      content:
        application/problem+json:
          schema:
            type: object
            properties:
              error:
                type: object
                properties:
                  name:
                    type: string
                    description: The error name.
                    example: instanceNotFoundError
                  message:
                    type: string
                    description: The error message.
                    example: We could not find the collection you are looking for
          example:
            error:
              name: instanceNotFoundError
              message: We could not find the collection you are looking for
    putCollection:
      description: Successful Response
      content:
        application/json:
          schema:
            type: object
            properties:
              collection:
                type: object
                properties:
                  id:
                    type: string
                    description: The collection's ID.
                    example: 12ece9e1-2abf-4edc-8e34-de66e74114d2
                  name:
                    type: string
                    description: The collection's name.
                    example: Test Collection
                  uid:
                    type: string
                    format: uid
                    description: The collection's unique ID.
                    example: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
          example:
            collection:
              id: 12ece9e1-2abf-4edc-8e34-de66e74114d2
              name: Test Collection
              uid: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
    collection400ErrorMalformedRequest:
      description: Bad Request
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                type: object
                properties:
                  name:
                    type: string
                    description: The error name.
                    example: malformedRequestError
                  message:
                    type: string
                    description: The error message.
                    example: Found 2 errors with the supplied collection.
                  details:
                    type: array
                    description: Information about the error.
                    items:
                      type: string
                      example: ': must have required property ''item'''
          example:
            error:
              name: malformedRequestError
              message: Found 2 errors with the supplied collection.
              details:
                - ': must have required property ''item'''
                - 'info: must have required property ''schema'''
    common403Error:
      description: Forbidden
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/common403Error'
          example:
            value:
              type: https://api.postman.com/problems/forbidden
              title: Forbidden
              detail: Forbidden
              status: 403
    deleteCollection:
      description: Successful Response
      content:
        application/json:
          schema:
            type: object
            properties:
              collection:
                type: object
                description: Information about the deleted collection.
                properties:
                  id:
                    type: string
                    description: The deleted collection's ID.
                    example: 12ece9e1-2abf-4edc-8e34-de66e74114d2
                  uid:
                    type: string
                    format: uid
                    description: The deleted collection's unique ID.
                    example: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
          example:
            collection:
              id: 12ece9e1-2abf-4edc-8e34-de66e74114d2
              uid: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
    patchCollection:
      description: Successful Response
      content:
        application/json:
          schema:
            type: object
            properties:
              collection:
                type: object
                properties:
                  id:
                    type: string
                    description: The collection's ID.
                    example: 12ece9e1-2abf-4edc-8e34-de66e74114d2
                  name:
                    type: string
                    description: The collection's updated name.
                    example: Test Collection v2
                  description:
                    type: string
                    description: The collection's updated description.
                    example: This is a sample collection that makes a request to the Postman Echo service. It returns a list of request headers sent by an HTTP client.
          example:
            collection:
              id: 12ece9e1-2abf-4edc-8e34-de66e74114d2
              name: Test Collection v2
              description: This is a sample collection that makes a request to the Postman Echo service. It returns a list of request headers sent by an HTTP client.
    collection400ErrorInvalidKeyParam:
      description: Bad Request
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                type: object
                properties:
                  name:
                    type: string
                    description: The error name.
                    example: badRequest
                  message:
                    type: string
                    description: The error message.
                    example: 'Invalid parameters: (''property'') for key: ''collection.variables'''
          example:
            error:
              name: badRequest
              message: 'Invalid parameters: (''property'') for key: ''collection.variables'''
    createCollectionFolder:
      description: Successful Response
      content:
        application/json:
          schema:
            type: object
            properties:
              data:
                type: object
                description: Information about the collection folder. For a complete list of properties, refer to the [Postman Collection Format documentation](https://schema.postman.com/collection/json/v2.1.0/draft-07/docs/index.html).
                additionalProperties: true
                properties:
                  id:
                    type: string
                    description: The folder's ID.
                    example: 65a99e60-8e0a-4b6e-b79c-7d8264cc5caa
                  name:
                    type: string
                    description: The folder's name.
                    example: Test Folder
                  order:
                    type: array
                    description: A list of request IDs and their order in the created folder.
                    items:
                      type: string
                    uniqueItems: true
                    example: []
                  owner:
                    type: string
                    description: The user ID of the folder's owner.
                    example: '********'
                  folder:
                    type: string
                    nullable: true
                    description: Information about the folder.
                    example: null
                  folders:
                    type: array
                    description: A list of folders.
                    items:
                      type: string
                    example: []
                  requests:
                    type: array
                    description: A list of requests.
                    items:
                      type: string
                  createdAt:
                    type: string
                    format: date-time
                    description: The folder's creation date and time.
                    example: '2022-08-29T16:49:19.964Z'
                  updatedAt:
                    type: string
                    format: date-time
                    description: The date and time at which the folder was last updated.
                    example: '2022-08-29T16:49:19.964Z'
                  collection:
                    type: string
                    description: The collection ID that the folder belongs to.
                    example: 12ece9e1-2abf-4edc-8e34-de66e74114d2
                  description:
                    type: string
                    description: The folder's description.
                    example: This is a test collection folder.
                  folders_order:
                    type: array
                    items:
                      type: string
                    uniqueItems: true
                    description: A list of folder IDs and their order in the collection.
                    example: []
                  lastUpdatedBy:
                    type: string
                    description: The user ID of the user that last updated the folder.
                    example: '********'
              meta:
                type: object
                description: A Postman-specific response that contains information about the internal performed operation.
                additionalProperties: true
              model_id:
                type: string
                description: The folder's ID.
                example: 65a99e60-8e0a-4b6e-b79c-7d8264cc5caa
              revision:
                type: number
                description: An internal revision ID. Its value increments each time the resource changes. You can use this ID to track whether there were changes since the last time you fetched the resource.
                example: 24994132707
          example:
            data:
              id: 65a99e60-8e0a-4b6e-b79c-7d8264cc5caa
              name: Test Folder
              order: []
              owner: '********'
              folder: null
              folders: []
              requests: []
              createdAt: '2022-08-29T16:49:19.964Z'
              updatedAt: '2022-08-29T16:49:19.964Z'
              collection: 12ece9e1-2abf-4edc-8e34-de66e74114d2
              description: This is a test collection folder.
              folders_order: []
              lastUpdatedBy: '********'
            meta:
              model: folder
              action: import
            model_id: 65a99e60-8e0a-4b6e-b79c-7d8264cc5caa
            revision: 24994132707
    collectionFolder400Error:
      description: Bad Request
      content:
        application/problem+json:
          schema:
            type: object
            properties:
              error:
                type: object
                properties:
                  name:
                    type: string
                    description: The error name.
                    example: instanceFoundError
                  details:
                    type: object
                    description: Information about the error.
                    additionalProperties: true
                    properties:
                      model:
                        type: string
                        description: The resource name.
                        example: folder
                      owner:
                        type: string
                        description: The user ID of the folder's owner.
                        example: '********'
                      model_id:
                        type: string
                        description: The folder's ID.
                        example: 65a99e60-8e0a-4b6e-b79c-7d8264cc5caa
                  message:
                    type: string
                    description: The error message.
                    example: This folder already exists in the database.
          example:
            error:
              name: instanceNotFoundError
              details:
                model: folder
                owner: '********'
                model_id: 65a99e60-8e0a-4b6e-b79c-7d8264cc5caa
              message: We could not find the folder you are looking for
    collectionFolder401Error:
      description: Unauthorized
      content:
        application/problem+json:
          schema:
            type: object
            properties:
              error:
                type: object
                properties:
                  name:
                    type: string
                    description: The error name.
                    example: Unauthorized
                  message:
                    type: string
                    description: The error message.
                    example: Unauthorized
                  details:
                    type: object
                    description: Information about the error.
                    properties:
                      model:
                        type: string
                        description: The resource name.
                        example: folder
                      owner:
                        type: string
                        description: The user ID of the folder's owner.
                        example: '********'
                      model_id:
                        type: string
                        description: The folder's ID.
                        example: 65a99e60-8e0a-4b6e-b79c-7d8264cc5caa
          example:
            error:
              name: Unauthorized
              message: Unauthorized
              details:
                model: folder
                model_id: 65a99e60-8e0a-4b6e-b79c-7d8264cc5caa
                owner: '********'
    getCollectionForks:
      description: Successful Response
      content:
        application/json:
          schema:
            type: object
            properties:
              data:
                type: array
                description: A list of the collection's forks.
                items:
                  type: object
                  description: Information about the forked collection.
                  properties:
                    createdAt:
                      type: string
                      format: date-time
                      description: The date and time at which the fork was created.
                      example: '2023-11-16T09:18:17.000Z'
                    createdBy:
                      type: string
                      description: The user who created the collection fork.
                      example: Taylor Lee
                    forkId:
                      type: string
                      format: uid
                      description: The forked collection's ID.
                      example: ********-667df1ad-2ee3-7890-b678-7742d82e2e1f
                    forkName:
                      type: string
                      description: The forked collection's label.
                      example: Collection Fork
              meta:
                type: object
                description: The response's meta information for paginated results.
                properties:
                  nextCursor:
                    type: string
                    nullable: true
                    format: base64
                    description: The pagination cursor that points to the next record in the results set.
                    example: eyJpZCI6MzMyNzksImZvcmtzQWZ0ZXIiOjIwfQ==
                  total:
                    type: number
                    description: The total number of forked collections.
                    example: 2
          example:
            data:
              - forkName: Collection Fork
                forkId: ********-667df1ad-2ee3-7890-b678-7742d82e2e1f
                createdBy: Taylor Lee
                createdAt: '2023-11-16T09:18:17.000Z'
              - forkName: Test Fork
                forkId: 87654321-38j9f1ad-2ee3-7890-dv5h-7742d82e2e1f
                createdBy: Alex Cruz
                createdAt: '2023-11-16T09:16:15.000Z'
            meta:
              total: 2
              nextCursor: null
    forkCollection400ErrorNoForks:
      description: Bad Request
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                type: object
                properties:
                  name:
                    type: string
                    description: The error name.
                    example: badRequest
                  message:
                    type: string
                    description: The error message.
                    example: This collection id has no forks.
          example:
            collection:
              name: badRequest
              message: This collection id has no forks.
    fork404Error:
      description: Not Found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/fork404Error'
          example:
            collection:
              name: notFoundError
              message: Fork not found.
    pullCollectionChanges:
      description: Successful Response
      content:
        application/json:
          schema:
            type: object
            properties:
              collection:
                type: object
                description: Information about the updated collection fork.
                properties:
                  destinationId:
                    type: string
                    format: uid
                    description: The ID of the forked collection the changes were pulled into.
                    example: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
                  sourceId:
                    type: string
                    format: uid
                    description: The ID of the source collection the changes were pulled from.
                    example: ********-45499451-e894-41b8-8c66-8191ab64932a
          example:
            collection:
              destinationId: 1********-12ece9e1-2abf-4edc-8e34-de66e74114d2
              sourceId: ********-45499451-e894-41b8-8c66-8191ab64932a
    forkCollection400ErrorBadId:
      description: Bad Request
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                type: object
                properties:
                  name:
                    type: string
                    description: The error name.
                    example: badRequest
                  message:
                    type: string
                    description: The error message.
                    example: The collection Id is not a forked collection.
          example:
            collection:
              name: badRequest
              message: The collection Id is not a forked collection.
    getCollectionPullRequests:
      description: Successful Response
      content:
        application/json:
          schema:
            type: object
            properties:
              data:
                type: array
                items:
                  type: object
                  description: Information about the pull request.
                  properties:
                    createdAt:
                      type: string
                      description: The date and time at which the pull request was created.
                      example: '2024-02-20T09:55:46.000Z'
                    createdBy:
                      type: string
                      description: The ID of the user who created the pull request.
                      example: '********'
                    description:
                      type: string
                      description: The pull request's description.
                      example: This is an open pull request.
                    destinationId:
                      type: string
                      description: The pull request's merge destination ID.
                      example: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
                    href:
                      type: string
                      description: A URL where you can view the pull request's details.
                      example: /pull-requests/4e1a6609-1a29-4037-a411-89ecc14c6cd8
                    id:
                      type: string
                      description: The pull request's ID.
                      example: 4e1a6609-1a29-4037-a411-89ecc14c6cd8
                    sourceId:
                      type: string
                      description: The pull request's source (parent) ID.
                      example: 87654321-3b79068c-dbe5-41d5-a826-51be4bf646ef
                    status:
                      type: string
                      description: The pull request's current status.
                      enum:
                        - open
                        - approved
                        - declined
                        - merged
                      example: open
                    comment:
                      type: string
                      description: If the pull request is declined, a comment about why the pull request was declined.
                      example: Missing information.
                    title:
                      type: string
                      description: The pull request's title.
                      example: Open PR
                    updatedBy:
                      type: string
                      description: The ID of the user who updated the pull request.
                      example: '********'
                    updatedAt:
                      type: string
                      format: date-time
                      description: The date and time at which the pull request was updated.
                      example: '2024-02-20T09:58:38.000Z'
          example:
            data:
              - createdAt: '2024-02-20T09:55:46.000Z'
                createdBy: '********'
                description: This is an open pull request.
                destinationId: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
                href: /pull-requests/4e1a6609-1a29-4037-a411-89ecc14c6cd8
                id: 4e1a6609-1a29-4037-a411-89ecc14c6cd8
                sourceId: 87654321-3b79068c-dbe5-41d5-a826-51be4bf646ef
                status: open
                title: Open PR
                updatedBy: '********'
                updatedAt: '2024-02-20T09:58:38.000Z'
              - createdAt: '2024-02-20T09:58:57.000Z'
                createdBy: '********'
                description: This is an approved PR.
                destinationId: ********-24f57217-1169-4b7c-a810-0e957c04eaa5
                href: /pull-requests/ac959187-568f-4e5e-9d17-584eb44fbed2
                id: ac959187-568f-4e5e-9d17-584eb44fbed2
                sourceId: 87654321-e50a18a3-0dcd-49cb-afa5-d3a82cdf6134
                status: approved
                title: Approved PR
                updatedBy: '********'
                updatedAt: '2024-02-20T09:58:57.000Z'
              - comment: Missing information.
                createdAt: '2024-02-20T09:55:46.000Z'
                createdBy: '********'
                description: This is a declined PR.
                destinationId: ********-c7f30bd9-5cc7-4569-a5fe-92270af33c6c
                href: /pull-requests/eda21e1f-8688-43aa-8660-35337dbac04b
                id: eda21e1f-8688-43aa-8660-35337dbac04b
                sourceId: 87654321-4beee503-1a7a-4925-a165-16fc29eaad22
                status: declined
                title: Declined PR
                updatedBy: '********'
                updatedAt: '2024-02-20T09:58:38.000Z'
    pullRequest403ErrorRolePermissionsCollection:
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/pullRequest403ErrorRolePermissionsCollection'
          example:
            detail: You need Viewer role on both source and destination collections to view pull requests. Please contact collection editors to grant additional access to you.
            status: 403
            title: Forbidden
            type: https://api.postman.com/problems/forbidden
    pullRequestCreated:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/pullRequestCreated'
          example:
            id: 4e1a6609-1a29-4037-a411-89ecc14c6cd8
            title: Test PR
            status: open
            sourceId: 87654321-3b79068c-dbe5-41d5-a826-51be4bf646ef
            createdAt: '2024-02-20T09:55:46.000Z'
            createdBy: '********'
            updatedAt: '2024-02-20T09:58:57.000Z'
            description: This is a test pull request.
            destinationId: ********-ec548788-unftw-rgn8-83b8-0b59798648e4
    pullRequest403ErrorNoViewerAccessCollections:
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/pullRequest403ErrorNoViewerAccessCollections'
          example:
            detail: You and all reviewers need Viewer access on both source and destination collections.
            status: 403
            title: Forbidden
            type: https://api.postman.com/problems/forbidden
    createCollectionRequest:
      description: Successful Response
      content:
        application/json:
          schema:
            type: object
            properties:
              data:
                type: object
                additionalProperties: true
                description: Information about the created request. For a complete list of properties, refer to the **Request** entry in the [Postman Collection Format documentation](https://schema.postman.com/collection/json/v2.1.0/draft-07/docs/index.html).
                properties:
                  id:
                    type: string
                    description: The request's ID.
                    example: c82dd02c-4870-4907-8fcb-593a876cf05b
                  name:
                    type: string
                    description: The request's name.
                    example: Test Request
                  owner:
                    type: string
                    description: The user ID of the request's owner.
                    example: '********'
                  folder:
                    type: string
                    nullable: true
                    description: Information about the request's parent folder.
                    example: null
                  responses:
                    type: array
                    description: A list of the request's responses.
                    items:
                      type: string
                    example: []
                  collection:
                    type: string
                    description: The collection ID that the request belongs to.
                    example: 12ece9e1-2abf-4edc-8e34-de66e74114d2
                  responses_order:
                    type: array
                    description: A list of response IDs and their order in the folder.
                    items:
                      type: string
                    uniqueItems: true
                    example: []
                  createdAt:
                    type: string
                    format: date-time
                    description: The requeset's creation date and time.
                    example: '2022-08-29T16:35:38.831Z'
                  updatedAt:
                    type: string
                    format: date-time
                    description: The date and time at which the request was last updated.
                    example: '2022-08-29T16:35:38.831Z'
                  lastUpdatedBy:
                    type: string
                    description: The user ID of the user that last updated the folder.
                    example: '********'
              meta:
                type: object
                description: A Postman-specific response that contains information about the internal performed operation.
                additionalProperties: true
              model_id:
                type: string
                description: The request's ID.
                example: c82dd02c-4870-4907-8fcb-593a876cf05b
              revision:
                type: number
                description: An internal revision ID. Its value increments each time the resource changes. You can use this ID to track whether there were changes since the last time you fetched the resource.
                example: 26531992398
          example:
            model_id: c82dd02c-4870-4907-8fcb-593a876cf05b
            meta:
              model: request
              action: import
            data:
              collection: 12ece9e1-2abf-4edc-8e34-de66e74114d2
              method: GET
              headers:
                - key: Host
                  value: postman-echo.com
                - key: user-agent
                  value: curl/7.88.1
                - key: accept
                  value: '*/*'
                - key: content-type
                  value: application/json
              dataMode: raw
              url: https://postman-echo.com/get?param=value
              name: Example GET Request
              description: This is an example GET request.
              owner: '********'
              folder: null
              responses_order: []
              id: c82dd02c-4870-4907-8fcb-593a876cf05b
              lastUpdatedBy: '********'
              queryParams:
                - key: id
                  value: '{{id}}'
                  equals: true
                  description: Optional. The user's ID.
                  enabled: true
              headerData: []
              createdAt: '2023-09-07T13:33:17.912Z'
              updatedAt: '2023-09-07T13:33:17.912Z'
              rawModeData: null
              dataOptions:
                raw:
                  language: json
              responses: []
            revision: 32456338071
    collectionRequest400Error:
      description: Bad Request
      content:
        application/problem+json:
          schema:
            type: object
            properties:
              error:
                type: object
                properties:
                  name:
                    type: string
                    description: The error name.
                    example: instanceFoundError
                  details:
                    type: object
                    description: Information about the error.
                    additionalProperties: true
                    properties:
                      model:
                        type: string
                        description: The resource name.
                        example: request
                      owner:
                        type: string
                        description: The user ID of the request's owner.
                        example: '********'
                      model_id:
                        type: string
                        description: The request's ID.
                        example: c82dd02c-4870-4907-8fcb-593a876cf05b
                  message:
                    type: string
                    description: The error message.
                    example: This request already exists in the database.
          example:
            error:
              name: instanceFoundError
              details:
                model: request
                owner: '********'
                model_id: c82dd02c-4870-4907-8fcb-593a876cf05b
              message: This request already exists in the database.
    collectionRequest401Error:
      description: Unauthorized
      content:
        application/problem+json:
          schema:
            type: object
            properties:
              error:
                type: object
                properties:
                  name:
                    type: string
                    description: The error name.
                    example: Unauthorized
                  details:
                    type: object
                    description: Information about the error.
                    properties:
                      model:
                        type: string
                        description: The resource name.
                        example: request
                      owner:
                        type: string
                        description: The user ID of the request's owner.
                        example: '********'
                      model_id:
                        type: string
                        description: The request's ID.
                        example: c82dd02c-4870-4907-8fcb-593a876cf05b
                  message:
                    type: string
                    description: The error message.
                    example: Unauthorized
          example:
            error:
              name: Unauthorized
              details:
                model: request
                owner: '********'
                model_id: c82dd02c-4870-4907-8fcb-593a876cf05b
              message: Unauthorized
    createCollectionResponse:
      description: Successful Response
      content:
        application/json:
          schema:
            type: object
            properties:
              data:
                type: object
                additionalProperties: true
                description: Information about the response. For a complete list of properties, refer to the **Response** entry in the [Postman Collection Format documentation](https://schema.postman.com/collection/json/v2.1.0/draft-07/docs/index.html).
                properties:
                  id:
                    type: string
                    description: The response's ID.
                    example: 43e1277f-6079-4feb-9316-c8ec24605092
                  owner:
                    type: string
                    description: The user ID of the response's owner.
                    example: '********'
                  request:
                    type: string
                    description: The request ID of the response's associated request.
                    example: c82dd02c-4870-4907-8fcb-593a876cf05b
                  createdAt:
                    type: string
                    format: date-time
                    description: The date and time at which the response was created.
                    example: '2022-08-19T15:06:18.940Z'
                  updatedAt:
                    type: string
                    format: date-time
                    description: The date and time at which the response was last updated.
                    example: '2022-08-19T15:06:18.940Z'
                  lastUpdatedBy:
                    type: string
                    description: The user ID of the user who last updated the response.
                    example: '********'
              meta:
                type: object
                description: A Postman-specific response that contains information about the internal performed operation.
                additionalProperties: true
              model_id:
                type: string
                description: The response's ID.
                example: 43e1277f-6079-4feb-9316-c8ec24605092
              revision:
                type: number
                description: An internal revision ID. Its value increments each time the resource changes. You can use this ID to track whether there were changes since the last time you fetched the resource.
                example: 26532286083
          example:
            data:
              id: 43e1277f-6079-4feb-9316-c8ec24605092
              owner: '********'
              request: c82dd02c-4870-4907-8fcb-593a876cf05b
              createdAt: '2022-08-29T16:58:15.523Z'
              updatedAt: '2022-08-29T16:58:15.523Z'
              lastUpdatedBy: '********'
            meta:
              model: response
              action: create
            model_id: 43e1277f-6079-4feb-9316-c8ec24605092
            revision: 26532286083
    collectionResponse400Error:
      description: Bad Request
      content:
        application/problem+json:
          schema:
            type: object
            properties:
              error:
                type: object
                properties:
                  name:
                    type: string
                    description: The error name.
                    example: instanceFoundError
                  details:
                    type: object
                    description: Information about the error.
                    additionalProperties: true
                    properties:
                      model:
                        type: string
                        description: The resource name.
                        example: response
                      owner:
                        type: string
                        description: The user ID of the response's owner.
                        example: '********'
                      model_id:
                        type: string
                        description: The response's ID.
                        example: 7978bfb8-fb89-42ac-8d48-cd547d96463e
                  message:
                    type: string
                    description: The error message.
                    example: This response already exists in the database.
          example:
            error:
              name: instanceFoundError
              message: This response already exists in the database.
              details:
                model: response
                model_id: 7978bfb8-fb89-42ac-8d48-cd547d96463e
                owner: '********'
    collectionResponse401Error:
      description: Unauthorized
      content:
        application/problem+json:
          schema:
            type: object
            properties:
              error:
                type: object
                properties:
                  name:
                    type: string
                    description: The error name.
                    example: Unauthorized
                  details:
                    type: object
                    description: Information about the error.
                    properties:
                      model:
                        type: string
                        description: The resource name.
                        example: response
                      owner:
                        type: string
                        description: The user ID of the response's owner.
                        example: '********'
                      model_id:
                        type: string
                        description: The response's ID.
                        example: 7978bfb8-fb89-42ac-8d48-cd547d96463e
                  message:
                    type: string
                    description: The error message.
                    example: Unauthorized
          example:
            error:
              name: Unauthorized
              details:
                model: response
                owner: '********'
                model_id: 7978bfb8-fb89-42ac-8d48-cd547d96463e
              message: Unauthorized
    getCollectionRoles:
      description: Successful Response
      content:
        application/json:
          schema:
            type: object
            description: Information about the collection's roles.
            properties:
              group:
                type: array
                description: A list of the collection's group roles.
                items:
                  type: object
                  description: Information about the group role.
                  properties:
                    role:
                      type: string
                      description: |
                        The role type:
                        - `VIEWER` — Can view, fork, and export collections.
                        - `EDITOR` — Can edit collections directly.
                      enum:
                        - VIEWER
                        - EDITOR
                      example: VIEWER
                    id:
                      type: number
                      description: The role's ID.
                      example: 123
              team:
                type: array
                description: A list of the collection's team roles.
                items:
                  type: object
                  description: Information about the team role.
                  properties:
                    role:
                      type: string
                      description: |
                        The role type:
                        - `VIEWER` — Can view, fork, and export collections.
                        - `EDITOR` — Can edit collections directly.
                      enum:
                        - VIEWER
                        - EDITOR
                      example: EDITOR
                    id:
                      type: number
                      description: The role's ID.
                      example: 1
              user:
                type: array
                description: A list of the collection's user roles.
                items:
                  type: object
                  description: Information about the user role.
                  properties:
                    role:
                      type: string
                      description: |
                        The role type:
                        - `VIEWER` — Can view, fork, and export collections.
                        - `EDITOR` — Can edit collections directly.
                      enum:
                        - VIEWER
                        - EDITOR
                      example: VIEWER
                    id:
                      type: number
                      description: The role's ID.
                      example: ********
          example:
            group:
              - role: VIEWER
                id: 123
            team:
              - role: EDITOR
                id: 1
            user:
              - role: VIEWER
                id: ********
              - role: EDITOR
                id: 87654321
    common403ErrorPermissions:
      description: Forbidden
      content:
        application/problem+json:
          schema:
            type: object
            properties:
              type:
                type: string
                format: uri-reference
                description: The [URI reference](https://www.rfc-editor.org/rfc/rfc3986) that identifies the type of problem.
                example: https://api.postman.com/problems/forbidden
              title:
                type: string
                description: A short summary of the problem.
                example: Resource cannot be accessed
              detail:
                type: string
                description: Information about the error.
                example: Inadequate permissions. Resource access forbidden.
              status:
                type: number
                format: http-status-code
                description: The error's HTTP status code.
                example: 403
          example:
            type: https://api.postman.com/problems/forbidden
            title: Resource cannot be accessed
            detail: Inadequate permissions. Resource access forbidden.
            status: 403
    collection404ErrorInstanceNotFound:
      description: Instance Not Found
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                type: object
                properties:
                  title:
                    type: string
                    description: A short summary of the problem.
                    example: We could not find the collection you are looking for
                  type:
                    type: string
                    description: The type of error.
                    example: instanceNotFoundError
          example:
            title: We could not find the collection you are looking for
            type: instanceNotFoundError
    common500ErrorInternalServer:
      description: Internal Server Error
      content:
        application/problem+json:
          schema:
            type: object
            properties:
              type:
                type: string
                format: uri-reference
                description: The [URI reference](https://www.rfc-editor.org/rfc/rfc3986) that identifies the type of problem.
                example: https://api.postman.com/problems/internal-server-error
              title:
                type: string
                description: A short summary of the problem.
                example: Internal Sever Error
              detail:
                type: string
                description: An explanation about the problem.
                example: Internal Sever Error
              status:
                type: integer
                format: http-status-code
                description: The HTTP status code generated by the origin server.
                example: 500
          example:
            type: https://api.postman.com/problems/internal-server-error
            title: Internal Server Error
            detail: Internal Server Error
            status: 500
    collectionRoles400ErrorMissingProperty:
      description: Bad Request
      content:
        application/problem+json:
          schema:
            type: object
            properties:
              error:
                type: object
                properties:
                  type:
                    type: string
                    format: uri-reference
                    description: The [URI reference](https://www.rfc-editor.org/rfc/rfc3986) that identifies the type of problem.
                    example: https://api.postman.com/problems/bad-request
                  title:
                    type: string
                    description: A short summary of the problem.
                    example: 'Missing properties: ''path'''
                  detail:
                    type: string
                    description: Information about the error.
                    example: 'PATCH request body for ''/collections/12ece9e1-2abf-4edc-8e34-de66e74114d2/roles/roles'' failed to validate schema. Location: /properties/roles/items/required'
                  status:
                    type: number
                    format: http-status-code
                    description: The error's HTTP status code.
                    example: 400
          example:
            type: https://api.postman.com/problems/bad-request
            title: 'Missing properties: ''path'''
            detail: 'PATCH request body for ''/collections/12ece9e1-2abf-4edc-8e34-de66e74114d2/roles/roles'' failed to validate schema. Location: /properties/roles/items/required'
            status: 400
    getSourceCollectionStatus:
      description: Successful Response
      content:
        application/json:
          schema:
            type: object
            properties:
              collection:
                type: object
                properties:
                  collectionUid:
                    type: object
                    description: Information about the forked collection. The object's name is the forked collection's UID (`userId`-`collectionId`).
                    properties:
                      isSourceAhead:
                        type: boolean
                        description: If true, there is a difference between the forked collection and its source collection.
                        example: false
          example:
            collection:
              ********-12ece9e1-2abf-4edc-8e34-de66e74114d2:
                isSourceAhead: false
    forkCollection400ErrorNotForked:
      description: Bad Request
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                type: object
                properties:
                  name:
                    type: string
                    description: The error name.
                    example: badRequest
                  message:
                    type: string
                    description: The error message.
                    example: The collection is not a forked collection.
          example:
            collection:
              name: badRequest
              message: The collection is not a forked collection.
    pullRequest403ErrorForbidden:
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/pullRequest403ErrorForbidden'
          example:
            detail: You do not have enough permissions to perform this action.
            status: 403
            title: Forbidden
            type: https://api.postman.com/problems/forbidden
    getCollectionFolder:
      description: Successful Response
      content:
        application/json:
          schema:
            type: object
            properties:
              model_id:
                type: string
                description: The folder's ID.
                example: 65a99e60-8e0a-4b6e-b79c-7d8264cc5caa
              meta:
                type: object
                description: A Postman-specific response that contains information about the internal performed operation.
                additionalProperties: true
              data:
                type: object
                description: Information about the folder. For a complete list of properties, refer to the **Folders** entry in the [Postman Collection Format documentation](https://schema.postman.com/collection/json/v2.1.0/draft-07/docs/index.html).
                additionalProperties: true
                properties:
                  id:
                    type: string
                    description: The folder's ID.
                    example: 65a99e60-8e0a-4b6e-b79c-7d8264cc5caa
                  name:
                    type: string
                    description: The folder's name.
                    example: Test Folder
                  description:
                    type: string
                    description: The folder's description.
                    example: This is a test collection folder.
                  createdAt:
                    type: string
                    format: date-time
                    description: The folder's creation date and time.
                    example: '2022-08-29T16:49:19.964Z'
                  updatedAt:
                    type: string
                    format: date-time
                    description: The date and time at which the folder was last updated.
                    example: '2022-08-29T16:49:19.964Z'
                  owner:
                    type: string
                    description: The user ID of the folder's owner.
                    example: '********'
                  lastUpdatedBy:
                    type: string
                    description: The user ID of the user that last updated the folder.
                    example: '********'
                  lastRevision:
                    type: integer
                    format: int64
                    description: An internal revision ID. Its value increments each time the resource changes. You can use this ID to track whether there were changes since the last time you fetched the resource.
                    example: 24994132707
                  collection:
                    type: string
                    description: The collection ID that the folder belongs to.
                    example: 12ece9e1-2abf-4edc-8e34-de66e74114d2
          example:
            model_id: 65a99e60-8e0a-4b6e-b79c-7d8264cc5caa
            meta:
              model: folder
              populate: false
              changeset: false
              action: find
            data:
              owner: '********'
              collection: 12ece9e1-2abf-4edc-8e34-de66e74114d2
              folder: null
              id: 65a99e60-8e0a-4b6e-b79c-7d8264cc5caa
              name: Test Folder
              description: This is a test collection folder.
              variables: null
              auth:
                type: apikey
                apikey:
                  - key: value
                    value: '{{apiKey}}'
                  - key: key
                    value: X-Api-Key
                  - key: in
                    value: header
              events: null
              order:
                - 81b49e05-0b87-4ca4-ac8c-091aaedafea3
                - 929acf24-4234-45e1-59cf-dc2b27ea7603
                - 3cfc2ac8-00a9-47d2-415d-049773f23268
              folders_order:
                - 65a99e60-8e0a-4b6e-b79c-7d8264cc5caa
                - 5c341de9-5751-461f-b7bd-af86bbae740c
              createdAt: '2022-08-29T16:49:19.964Z'
              updatedAt: '2022-08-29T16:49:19.964Z'
    collectionFolder404Error:
      description: Not Found
      content:
        application/problem+json:
          schema:
            type: object
            properties:
              error:
                type: object
                properties:
                  name:
                    type: string
                    description: The error name.
                    example: instanceNotFoundError
                  message:
                    type: string
                    description: The error message.
                    example: We could not find the folder you are looking for
                  details:
                    type: object
                    description: Information about the error.
                    properties:
                      model:
                        type: string
                        description: The resource name.
                        example: folder
                      owner:
                        type: string
                        description: The user ID of the folder's owner.
                        example: '********'
                      model_id:
                        type: string
                        description: The folder's ID.
                        example: 65a99e60-8e0a-4b6e-b79c-7d8264cc5caa
          example:
            error:
              name: instanceNotFoundError
              message: We could not find the folder you are looking for
              details:
                model: folder
                owner: '********'
                model_id: 65a99e60-8e0a-4b6e-b79c-7d8264cc5caa
    updateCollectionFolder:
      description: Successful Response
      content:
        application/json:
          schema:
            type: object
            properties:
              data:
                type: object
                additionalProperties: true
                description: The folder's updated information, including the updated properties. For a complete list of properties, refer to the [Postman Collection Format documentation](https://schema.postman.com/collection/json/v1.0.0/draft-07/collection.json).
                properties:
                  id:
                    type: string
                    description: The folder's ID.
                    example: 65a99e60-8e0a-4b6e-b79c-7d8264cc5caa
                  name:
                    type: string
                    description: The folder's name.
                    example: Test Folder
                  description:
                    type: string
                    description: The folder's description.
                    example: This is a test collection folder.
                  createdAt:
                    type: string
                    format: date-time
                    description: The folder's creation date and time.
                    example: '2022-08-29T16:49:19.964Z'
                  updatedAt:
                    type: string
                    format: date-time
                    description: The date and time at which the folder was last updated.
                    example: '2022-08-29T16:49:19.964Z'
                  owner:
                    type: string
                    description: The user ID of the folder's owner.
                    example: '********'
                  lastUpdatedBy:
                    type: string
                    description: The user ID of the user that last updated the folder.
                    example: '********'
                  lastRevision:
                    type: integer
                    format: int64
                    description: An internal revision ID. Its value increments each time the resource changes. You can use this ID to track whether there were changes since the last time you fetched the resource.
                    example: 24977100955
                  collection:
                    type: string
                    description: The collection ID that the folder belongs to.
                    example: 12ece9e1-2abf-4edc-8e34-de66e74114d2
                  folder:
                    type: string
                    nullable: true
                    description: Information about the folder.
                    example: null
              meta:
                type: object
                description: A Postman-specific response that contains information about the internal performed operation.
                additionalProperties: true
              model_id:
                type: string
                description: The folder's ID.
                example: 65a99e60-8e0a-4b6e-b79c-7d8264cc5caa
              revision:
                type: number
                description: An internal revision ID. Its value increments each time the resource changes. You can use this ID to track whether there were changes since the last time you fetched the resource.
                example: 24994132707
          example:
            data:
              id: 65a99e60-8e0a-4b6e-b79c-7d8264cc5caa
              auth: null
              name: Test Folder
              order: []
              owner: '********'
              events: null
              folder: null
              createdAt: '2022-08-29T16:49:19.964Z'
              updatedAt: '2022-08-29T16:49:19.964Z'
              variables: null
              collection: 12ece9e1-2abf-4edc-8e34-de66e74114d2
              description: This is a test folder.
              lastRevision: 24977100955
              folders_order: []
              lastUpdatedBy: '********'
            meta:
              model: folder
              action: update
            model_id: 65a99e60-8e0a-4b6e-b79c-7d8264cc5caa
            revision: 24977103385
    deleteCollectionFolder:
      description: Successful Response
      content:
        application/json:
          schema:
            type: object
            properties:
              data:
                type: object
                description: The folder's information.
                properties:
                  id:
                    type: string
                    description: The folder's ID.
                    example: 65a99e60-8e0a-4b6e-b79c-7d8264cc5caa
                  owner:
                    type: string
                    description: The user ID of the folder's owner.
                    example: '********'
              meta:
                type: object
                description: A Postman-specific response that contains information about the internal performed operation.
                additionalProperties: true
              model_id:
                type: string
                description: The folder's ID.
                example: 65a99e60-8e0a-4b6e-b79c-7d8264cc5caa
              revision:
                type: number
                description: An internal revision ID. Its value increments each time the resource changes. You can use this ID to track whether there were changes since the last time you fetched the resource.
                example: 24994562696
          example:
            model_id: 65a99e60-8e0a-4b6e-b79c-7d8264cc5caa
            meta:
              model: folder
              action: destroy
            data:
              id: 65a99e60-8e0a-4b6e-b79c-7d8264cc5caa
              owner: '********'
            revision: 24994562696
    getCollectionRequest:
      description: Successful Response
      content:
        application/json:
          schema:
            type: object
            properties:
              model_id:
                type: string
                description: The request's ID.
                example: 65a99e60-8e0a-4b6e-b79c-7d8264cc5caa
              meta:
                type: object
                description: A Postman-specific response that contains information about the internal performed operation.
                additionalProperties: true
              data:
                type: object
                additionalProperties: true
                description: Information about the request. For a complete list of properties, refer to the **Request** entry in the [Postman Collection Format documentation](https://schema.postman.com/collection/json/v2.1.0/draft-07/docs/index.html).
                properties:
                  id:
                    type: string
                    description: The request's ID.
                    example: c82dd02c-4870-4907-8fcb-593a876cf05b
                  name:
                    type: string
                    description: The request's name.
                    example: Test Folder
                  owner:
                    type: string
                    description: The user ID of the request's owner.
                    example: '********'
                  lastRevision:
                    type: integer
                    format: int64
                    description: An internal revision ID. Its value increments each time the resource changes. You can use this ID to track whether there were changes since the last time you fetched the resource.
                    example: 24994132707
                  lastUpdatedBy:
                    type: string
                    description: The user ID of the user that last updated the request.
                    example: '********'
                  createdAt:
                    type: string
                    format: date-time
                    description: The request's creation date and time.
                    example: '2022-08-29T16:49:19.964Z'
                  updatedAt:
                    type: string
                    format: date-time
                    description: The date and time at which the request was last updated.
                    example: '2022-08-29T16:49:19.964Z'
          example:
            model_id: c82dd02c-4870-4907-8fcb-593a876cf05b
            meta:
              model: request
              populate: false
              changeset: false
              action: find
            data:
              owner: '********'
              lastUpdatedBy: '********'
              lastRevision: 32206434799
              folder: null
              collection: 12ece9e1-2abf-4edc-8e34-de66e74114d2
              id: c82dd02c-4870-4907-8fcb-593a876cf05b
              name: Example GET Request
              dataMode: null
              data: null
              auth: null
              events: null
              rawModeData: null
              descriptionFormat: null
              description: This is an example GET request.
              variables: null
              headers: null
              method: GET
              pathVariables: {}
              url: https://postman-echo.com/get
              preRequestScript: null
              tests: var data = JSON.parse(responseBody);
              currentHelper: null
              helperAttributes: null
              queryParams:
                - key: id
                  value: '{{id}}'
                  equals: true
                  description: Optional. The user's ID.
                  enabled: true
              headerData: []
              pathVariableData: []
              protocolProfileBehavior:
                disableBodyPruning: true
              dataDisabled: false
              responses_order: []
              createdAt: '2023-08-22T12:52:01.000Z'
              updatedAt: '2023-08-22T12:52:11.000Z'
    collectionRequest404Error:
      description: Not Found
      content:
        application/problem+json:
          schema:
            type: object
            properties:
              error:
                type: object
                properties:
                  name:
                    type: string
                    description: The error name.
                    example: instanceNotFoundError
                  details:
                    type: object
                    description: Information about the error.
                    properties:
                      model:
                        type: string
                        description: The resource name.
                        example: request
                      owner:
                        type: string
                        description: The user ID of the request's owner.
                        example: '********'
                      model_id:
                        type: string
                        description: The request's ID.
                        example: c82dd02c-4870-4907-8fcb-593a876cf05b
                  message:
                    type: string
                    description: The error message.
                    example: We could not find the request you are looking for
          example:
            error:
              name: instanceFoundError
              details:
                model: request
                owner: '********'
                model_id: c82dd02c-4870-4907-8fcb-593a876cf05b
              message: We could not find the request you are looking for
    updateCollectionRequest:
      description: Successful Response
      content:
        application/json:
          schema:
            type: object
            properties:
              data:
                type: object
                additionalProperties: true
                description: Information about the updated request. For a complete list of properties, refer to the **Request** entry in the [Postman Collection Format documentation](https://schema.postman.com/collection/json/v2.1.0/draft-07/docs/index.html).
                properties:
                  id:
                    type: string
                    description: The request's ID.
                    example: c82dd02c-4870-4907-8fcb-593a876cf05b
                  name:
                    type: string
                    description: The request's name.
                    example: Example GET request
                  description:
                    type: string
                    description: The request's description.
                    example: This is an example GET request.
                  createdAt:
                    type: string
                    format: date-time
                    description: The request's creation date and time.
                    example: '2022-08-29T16:49:19.964Z'
                  updatedAt:
                    type: string
                    format: date-time
                    description: The date and time at which the request was last updated.
                    example: '2022-08-29T16:49:19.964Z'
                  owner:
                    type: string
                    description: The user ID of the request's owner.
                    example: '********'
                  lastUpdatedBy:
                    type: string
                    description: The user ID of the user that last updated the request.
                    example: '********'
                  lastRevision:
                    type: integer
                    format: int64
                    example: 24994863800
                    description: An internal revision ID. Its value increments each time the resource changes. You can use this ID to track whether there were changes since the last time you fetched the resource.
              meta:
                type: object
                description: A Postman-specific response that contains information about the internal performed operation.
                additionalProperties: true
              model_id:
                type: string
                description: The request's ID.
                example: 65a99e60-8e0a-4b6e-b79c-7d8264cc5caa
              revision:
                type: number
                description: An internal revision ID. Its value increments each time the resource changes. You can use this ID to track whether there were changes since the last time you fetched the resource.
                example: 24994954828
          example:
            model_id: c82dd02c-4870-4907-8fcb-593a876cf05b
            meta:
              model: request
              action: update
            data:
              id: c82dd02c-4870-4907-8fcb-593a876cf05b
              name: Example POST Request
              dataMode: raw
              data: null
              auth: noauth
              events: null
              rawModeData: |-
                {
                    "field": "Value"
                }
              descriptionFormat: null
              description: This is an example POST request.
              variables: null
              headers: null
              method: POST
              pathVariables: null
              url: https://postman-echo.com/post
              preRequestScript: null
              tests: null
              currentHelper: null
              helperAttributes: null
              queryParams: []
              headerData: []
              pathVariableData: []
              protocolProfileBehavior: null
              dataDisabled: false
              responses_order: []
              createdAt: '2023-09-01T07:59:25.000Z'
              updatedAt: '2023-09-08T12:30:29.000Z'
              owner: '********'
              lastUpdatedBy: '********'
              lastRevision: 32473087936
              folder: null
              collection: 12ece9e1-2abf-4edc-8e34-de66e74114d2
              dataOptions:
                raw:
                  language: json
            revision: 32473098909
    deleteCollectionRequest:
      description: Successful Response
      content:
        application/json:
          schema:
            type: object
            properties:
              model_id:
                type: string
                description: The request's ID.
                example: c82dd02c-4870-4907-8fcb-593a876cf05b
              meta:
                type: object
                description: A Postman-specific response that contains information about the internal performed operation.
                additionalProperties: true
              data:
                type: object
                description: The request's information.
                properties:
                  id:
                    type: string
                    description: The request's ID.
                    example: c82dd02c-4870-4907-8fcb-593a876cf05b
                  owner:
                    type: string
                    description: The user ID of the request's owner.
                    example: '********'
              revision:
                type: number
                description: An internal revision ID. Its value increments each time the resource changes. You can use this ID to track whether there were changes since the last time you fetched the resource.
                example: 24995158577
          example:
            model_id: c82dd02c-4870-4907-8fcb-593a876cf05b
            meta:
              model: request
              action: destroy
            data:
              id: c82dd02c-4870-4907-8fcb-593a876cf05b
              owner: '********'
            revision: 24995158577
    getCollectionResponse:
      description: Successful Response
      content:
        application/json:
          schema:
            type: object
            properties:
              data:
                type: object
                description: Information about the response. For a complete list of properties, refer to the **Response** entry in the [Postman Collection Format documentation](https://schema.postman.com/collection/json/v2.1.0/draft-07/docs/index.html).
                additionalProperties: true
                properties:
                  id:
                    type: string
                    description: The response's ID.
                    example: cc364734-7dfd-4bfc-897d-be763dcdbb07
                  request:
                    type: string
                    description: The ID of the request that the response belongs to.
                    example: c82dd02c-4870-4907-8fcb-593a876cf05b
                  name:
                    type: string
                    description: The response's name.
                    example: Test Folder
                  owner:
                    type: string
                    description: The user ID of the response's owner.
                    example: '********'
                  createdAt:
                    type: string
                    format: date-time
                    description: The response's creation date and time.
                    example: '2022-08-29T16:49:19.964Z'
                  updatedAt:
                    type: string
                    format: date-time
                    description: The date and time at which the response was last updated.
                    example: '2022-08-29T16:49:19.964Z'
                  lastRevision:
                    type: integer
                    format: int64
                    description: An internal revision ID. Its value increments each time the resource changes. You can use this ID to track whether there were changes since the last time you fetched the resource.
                    example: 24994132707
                  lastUpdatedBy:
                    type: string
                    description: The user ID of the user that last updated the response.
                    example: '********'
              meta:
                type: object
                description: A Postman-specific response that contains information about the internal performed operation.
                additionalProperties: true
              model_id:
                type: string
                description: The response's ID.
                example: 65a99e60-8e0a-4b6e-b79c-7d8264cc5caa
          example:
            model_id: cc364734-7dfd-4bfc-897d-be763dcdbb07
            meta:
              model: response
              populate: false
              changeset: false
              action: find
            data:
              owner: '********'
              lastUpdatedBy: '********'
              lastRevision: 32457792182
              request: c82dd02c-4870-4907-8fcb-593a876cf05b
              id: cc364734-7dfd-4bfc-897d-be763dcdbb07
              name: Successful GET Response
              status: null
              responseCode:
                code: 200
                name: OK
              time: null
              headers:
                - key: Content-Type
                  name: Content-Type
                  value: application/json
                  description: ''
                  type: text
              cookies: null
              mime: null
              text: |-
                {
                    "title": "Not Found",
                    "detail": "Requested API does not exist",
                    "type": "about:blank"
                }
              language: json
              rawDataType: null
              requestObject: '{"data":null,"dataMode":"raw","dataOptions":{"raw":{"language":"json"}},"headerData":[{"key":"Accept","value":"application/vnd.example.v1+json","description":"","type":"text","enabled":true}],"method":"GET","pathVariableData":[],"queryParams":[{"key":"id","value":"test-api","equals":true,"description":null,"enabled":true}],"url":"http://api.getpostman.com/v1/request?id=test-api","headers":"Accept: application/vnd.example.v1+json\n","pathVariables":{},"protocolProfileBehavior":{"disableBodyPruning":true},"rawModeData":"","graphqlModeData":{}}'
              createdAt: '2023-09-07T14:39:52.000Z'
              updatedAt: '2023-09-07T14:41:08.000Z'
    collectionResponse404Error:
      description: Not Found
      content:
        application/problem+json:
          schema:
            type: object
            properties:
              error:
                type: object
                properties:
                  name:
                    type: string
                    description: The error name.
                    example: instanceNotFoundError
                  details:
                    type: object
                    description: Information about the error.
                    properties:
                      model:
                        type: string
                        description: The resource name.
                        example: response
                      owner:
                        type: string
                        description: The user ID of the response's owner.
                        example: '********'
                      model_id:
                        type: string
                        description: The response's ID.
                        example: 7978bfb8-fb89-42ac-8d48-cd547d96463e
                  message:
                    type: string
                    description: The error message.
                    example: We could not find the response you are looking for
          example:
            error:
              name: instanceNotFoundError
              message: We could not find the response you are looking for
              details:
                model: response
                model_id: 7978bfb8-fb89-42ac-8d48-cd547d96463e
                owner: '********'
    updateCollectionResponse:
      description: Successful Response
      content:
        application/json:
          schema:
            type: object
            properties:
              data:
                type: object
                description: Information about the updated response. For a complete list of response properties, refer to the **Response** entry in the [Postman Collection Format documentation](https://schema.postman.com/collection/json/v2.1.0/draft-07/docs/index.html).
                additionalProperties: true
                properties:
                  id:
                    type: string
                    description: The response's ID.
                    example: c82dd02c-4870-4907-8fcb-593a876cf05b
                  name:
                    type: string
                    description: The response's name.
                    example: Test Folder
                  owner:
                    type: string
                    description: The user ID of the response's owner.
                    example: '********'
                  createdAt:
                    type: string
                    format: date-time
                    description: The response's creation date and time.
                    example: '2022-08-29T16:49:19.964Z'
                  updatedAt:
                    type: string
                    format: date-time
                    description: The date and time at which the response was last updated.
                    example: '2022-08-29T16:49:19.964Z'
                  lastRevision:
                    type: integer
                    format: int64
                    description: An internal revision ID. Its value increments each time the resource changes. You can use this ID to track whether there were changes since the last time you fetched the resource.
                    example: 24994132707
                  lastUpdatedBy:
                    type: string
                    description: The user ID of the user that last updated the response.
                    example: '********'
              meta:
                type: object
                description: A Postman-specific response that contains information about the internal performed operation.
                additionalProperties: true
              model_id:
                type: string
                description: The response's ID.
                example: 65a99e60-8e0a-4b6e-b79c-7d8264cc5caa
          example:
            model_id: c82dd02c-4870-4907-8fcb-593a876cf05b
            meta:
              model: response
              action: update
            data:
              id: c82dd02c-4870-4907-8fcb-593a876cf05b
              name: Example Response
              status: null
              responseCode:
                name: OK
                code: 200
              time: '50'
              headers:
                - value: Fri, 01 Sep 2023 07:36:18 GMT
                  key: Date
                - value: application/json; charset=utf-8
                  key: Content-Type
                - value: '607'
                  key: Content-Length
                - value: keep-alive
                  key: Connection
              cookies: null
              mime: null
              text: |-
                {
                    "args": {},
                    "data": {
                        "field": "Value"
                    },
                    "files": {},
                    "form": {},
                    "headers": {
                        "x-forwarded-proto": "https",
                        "x-forwarded-port": "443",
                        "host": "postman-echo.com",
                        "x-amzn-trace-id": "Root=1-64f9cd12-62f18f3a048796d345508073",
                        "content-length": "24",
                        "content-type": "application/json",
                        "x-api-key": "xxx",
                        "user-agent": "PostmanRuntime/7.32.3",
                        "accept": "*/*",
                        "postman-token": "XXX",
                        "accept-encoding": "gzip, deflate, br"
                    },
                    "json": {
                        "field": "Value"
                    },
                    "url": "https://postman-echo.com/post"
                }
              language: json
              rawDataType: null
              requestObject: '{"data":null,"dataMode":"raw","dataOptions":{"raw":{"language":"json"}},"headerData":[],"method":"POST","pathVariableData":[],"queryParams":[],"url":"https://postman-echo.com/post","headers":"","pathVariables":{},"protocolProfileBehavior":{"disableBodyPruning":true},"rawModeData":"{\n    \"field\": \"Value\"\n}","graphqlModeData":{}}'
              createdAt: '2023-09-01T09:29:54.000Z'
              updatedAt: '2023-09-08T12:37:33.000Z'
              owner: '********'
              lastUpdatedBy: '********'
              lastRevision: 32368133468
              request: c82dd02c-4870-4907-8fcb-593a876cf05b
            revision: 32473225008
    deleteCollectionResponse:
      description: Successful Response
      content:
        application/json:
          schema:
            type: object
            properties:
              model_id:
                type: string
                description: The response's ID.
                example: cc364734-7dfd-4bfc-897d-be763dcdbb07
              meta:
                type: object
                description: A Postman-specific response that contains information about the internal performed operation.
                additionalProperties: true
              data:
                type: object
                description: The response's information.
                properties:
                  id:
                    type: string
                    description: The response's ID.
                    example: cc364734-7dfd-4bfc-897d-be763dcdbb07
                  owner:
                    type: string
                    description: The user ID of the request's owner.
                    example: '********'
              revision:
                type: number
                description: An internal revision ID. Its value increments each time the resource changes. You can use this ID to track whether there were changes since the last time you fetched the resource.
                example: 24997417224
          example:
            model_id: cc364734-7dfd-4bfc-897d-be763dcdbb07
            meta:
              model: response
              action: destroy
            data:
              id: cc364734-7dfd-4bfc-897d-be763dcdbb07
              owner: '********'
            revision: 24997417224
    tag403Error:
      description: Forbidden
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/tag403Error'
          example:
            type: https://api.postman.com/problems/forbidden
            title: Access Denied
            detail: The tags feature is only available to enterprise teams
            status: 403
    tag400Error:
      description: Bad Request
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/tag400Error'
          example:
            title: Bad Request
            detail: body/tags/0/slug must NOT have more than 64 characters
            status: 400
    transformCollectionToOpenAPI:
      description: Successful Response
      content:
        application/json:
          schema:
            type: object
            properties:
              output:
                type: string
                description: The collection's transformed output, in a stringified OpenAPI format.
                example: |
                  {
                      "openapi": "3.0.3",
                      "info": {
                          "title": "Collection to API",
                          "version": "1.0.0",
                          "contact": {}
                      },
                      "servers": [
                          {
                              "url": "http://api.getpostman.com/"
                          }
                      ],
                      "paths": {},
                      "tags": []
                  }
          example:
            output: |
              {
                  "openapi": "3.0.3",
                  "info": {
                      "title": "Collection to API",
                      "version": "1.0.0",
                      "contact": {}
                  },
                  "servers": [
                      {
                          "url": "http://api.getpostman.com/"
                      }
                  ],
                  "paths": {},
                  "tags": []
              }
    collectionToApi401Error:
      description: Unauthorized
      content:
        application/problem+json:
          schema:
            type: object
            properties:
              type:
                type: string
                format: uri-reference
                description: The [URI reference](https://www.rfc-editor.org/rfc/rfc3986) that identifies the type of problem.
                example: https://api.postman.com/problems/unauthorized
              title:
                type: string
                description: A short summary of the problem.
                example: Unauthorized
              detail:
                type: string
                description: Information about the error.
                example: Unauthorized
              status:
                type: integer
                format: http-status-code
                description: The error's HTTP status code.
                example: 401
              instance:
                type: string
                description: The URI reference that identifies the specific occurrence of the problem.
                example: /collections/{collectionId}/apis
          example:
            type: https://api.postman.com/problems/unauthorized
            title: Unauthorized
            detail: Unauthorized
            status: 401
            instance: /collections/{collectionId}/apis
    collectionToApi404Error:
      description: Not Found
      content:
        application/problem+json:
          schema:
            type: object
            properties:
              type:
                type: string
                format: uri-reference
                description: The [URI reference](https://www.rfc-editor.org/rfc/rfc3986) that identifies the type of problem.
                example: https://api.postman.com/problems/not-found
              title:
                type: string
                description: A short summary of the problem.
                example: Not Found
              detail:
                type: string
                description: Information about the error.
                example: Not Found
              status:
                type: integer
                format: http-status-code
                description: The error's HTTP status code.
                example: 404
              instance:
                type: string
                description: The URI reference that identifies the specific occurrence of the problem.
                example: /collections/{collectionId}/apis
          example:
            type: https://api.postman.com/problems/not-found
            title: Not Found
            detail: Not Found
            status: 404
            instance: /collections/{collectionId}/apis
    collectionToApi500Error:
      description: Internal Server Error
      content:
        application/problem+json:
          schema:
            type: object
            properties:
              type:
                type: string
                format: uri-reference
                description: The [URI reference](https://www.rfc-editor.org/rfc/rfc3986) that identifies the type of problem.
                example: https://api.postman.com/problems/internal-server-error
              title:
                type: string
                description: A short summary of the problem.
                example: Internal Server Error
              detail:
                type: string
                description: Information about the error.
                example: Internal Server Error
              status:
                type: integer
                format: http-status-code
                description: The error's HTTP status code.
                example: 500
              instance:
                type: string
                description: The URI reference that identifies the specific occurrence of the problem.
                example: /collections/{collectionId}/apis
          example:
            type: https://api.postman.com/problems/internal-server-error
            title: Internal Server Error
            detail: Internal Server Error
            status: 500
            instance: /collections/{collectionId}/apis
    transferCollectionItems200Error:
      description: Successful Response
      content:
        application/json:
          schema:
            type: object
            properties:
              ids:
                type: array
                description: A list of the transferred collection request, response, or folder UIDs.
                items:
                  type: string
                  format: uid
                  example: ********-a9b481c1-3e78-4af7-8db0-dce3f3f3c105
          example:
            ids:
              - ********-a9b481c1-3e78-4af7-8db0-dce3f3f3c105
    transferCollectionItems400Error:
      description: Bad Request
      content:
        application/problem+json:
          schema:
            type: object
            properties:
              type:
                type: string
                format: uri-reference
                description: The [URI reference](https://www.rfc-editor.org/rfc/rfc3986) that identifies the type of problem.
                example: https://api.postman.com/problems/bad-request
              title:
                type: string
                description: A short summary of the problem.
                example: Cannot transfer response to target model type folder
              detail:
                type: object
                description: Information about the error.
                additionalProperties: true
          example:
            type: https://api.postman.com/problems/bad-request
            title: Cannot transfer response to target model type folder
            detail: {}
    detectedSecretsQueries:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/detectedSecretsQueries'
          examples:
            Successful Response:
              $ref: '#/components/examples/detectedSecretsQueryResponse'
            With include Query Parameter:
              $ref: '#/components/examples/detectedSecretsQueryIncludeParam'
            Filter by Resource Type:
              $ref: '#/components/examples/detectedSecretsQueryFilterByResource'
    detectedSecretsQuery400Errors:
      description: Bad Request
      content:
        application/json:
          schema:
            anyOf:
              - $ref: '#/components/schemas/secretScanner400Error'
              - $ref: '#/components/schemas/detectedSecret400Error'
          examples:
            Bad Request:
              $ref: '#/components/examples/secretScanner400Error'
            Invalid Filter:
              $ref: '#/components/examples/detectedSecrets400FilterError'
    secretScanner401Error:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/secretScanner401Error'
          example:
            instance: ''
            status: 401
            title: Unauthorized. Please try again with valid credentials.
            type: UNAUTHORIZED
    secretScanner403ErrorAndFeatureUnavailable:
      description: Forbidden
      content:
        application/json:
          schema:
            anyOf:
              - $ref: '#/components/schemas/secretScanner403Error'
              - $ref: '#/components/schemas/featureUnavailable403Error'
          examples:
            Forbidden:
              $ref: '#/components/examples/secretScanner403Error'
            Feature Unavailable:
              $ref: '#/components/examples/featureUnavailable403Error'
    secretScanner500Error:
      description: Internal Server Error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/secretScanner500Error'
          example:
            instance: ''
            status: 500
            title: An error occurred
            type: SERVER_ERROR
    updateSecretResolutions:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/updateSecretResolutions'
          example:
            secretHash: 50dbd2...
            workspaceId: 1f0df51a-8658-4ee8-a2a1-d2567dfa09a9
            resolution: ACCEPTED_RISK
            history:
              - actor: ********
                createdAt: '2024-09-18T18:46:26.000Z'
                resolution: FALSE_POSITIVE
              - actor: ********
                createdAt: '2024-09-18T18:43:11.000Z'
                resolution: ACCEPTED_RISK
    secretScanner400InvalidResolutionError:
      description: Invalid Resolution Type
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/secretScanner400Error'
          example:
            type: BAD_REQUEST
            title: '`resolution` must be one of FALSE_POSITIVE, ACCEPTED_RISK, REVOKED, ACTIVE'
            instance: TEST
            status: 400
    getSecretsLocations:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/getSecretsLocations'
          example:
            data:
              - isResourceDeleted: false
                leakedBy: ********
                location: Headers
                occurrences: 1
                parentResourceId: ********-14728df4-c7af-424f-b665-5047b7d25866
                resourceId: ********-e0df25e0-d245-40dd-89b5-68720d186d3f
                resourceType: example
                detectedAt: '2023-04-25T08:13:48.000Z'
                url: https://go.postman.co/build/workspace/80ab14ae-c17d-4fd6-88d5-99bf13f0b7f0/example/********-e0df25e0-d245-40dd-89b5-68720d186d3f
            meta:
              activityFeed:
                - resolvedBy: ********
                  status: ACTIVE
                  resolvedAt: '2023-04-25T11:18:07.000Z'
              cursor: MTIyNjY5Nw==
              limit: 2
              nextCursor: null
              obfuscatedSecret: PMAK-644781584627df042afa6655-******ba
              secretHash: 0096b35ef6621d7571f106fefee5b10e8ed360cc9bf04f343f267ca4ff65bb5d
              secretType: Postman API Key
              total: 3
    secretScanner400Error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/secretScanner400Error'
          example:
            instance: ''
            status: 400
            title: limit specified is invalid
            type: BAD_REQUEST
    getEnvironments:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/getEnvironments'
          example:
            environments:
              - id: 5daabc50-8451-43f6-922d-96b403b4f28e
                name: Test Environment
                createdAt: '2020-09-23T14:31:18.000Z'
                updatedAt: '2020-12-04T14:13:40.000Z'
                owner: '********'
                uid: ********-5daabc50-8451-43f6-922d-96b403b4f28e
                isPublic: false
              - id: 7d786cc8-142b-4d62-b5a5-872afc37ad16
                name: Environment Scanner
                createdAt: '2020-02-04T19:34:23.000Z'
                updatedAt: '2020-08-12T13:34:06.000Z'
                owner: '********'
                uid: ********-7d786cc8-142b-4d62-b5a5-872afc37ad16
                isPublic: false
    instanceNotFoundEnvironment:
      description: Instance Not Found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/instanceNotFoundEnvironment'
          example:
            error:
              name: instanceNotFoundError
              message: We could not find the environment you are looking for
    createEnvironment:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/createEnvironmentResponse'
          example:
            environment:
              id: 5daabc50-8451-43f6-922d-96b403b4f28e
              name: Test Environment
              uid: ********-5daabc50-8451-43f6-922d-96b403b4f28e
    environments400ErrorMalformedRequest:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/environments400ErrorMalformedRequest'
          example:
            error:
              name: malformedRequestError
              message: Found 1 errors with the supplied environment.
              details:
                - ': must have required property ''environment'''
    getEnvironment:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/getEnvironment'
          example:
            environment:
              id: 5daabc50-8451-43f6-922d-96b403b4f28e
              name: Test Environment
              owner: '********'
              createdAt: '2020-11-05T13:59:22.000Z'
              updatedAt: '2020-11-05T13:59:23.000Z'
              values:
                - key: collectionId
                  value: 12ece9e1-2abf-4edc-8e34-de66e74114d2
                  enabled: true
                  type: default
              isPublic: false
    updateEnvironment:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/updateEnvironmentResponse'
          example:
            environment:
              id: 5daabc50-8451-43f6-922d-96b403b4f28e
              name: Test A Environment
              uid: ********-5daabc50-8451-43f6-922d-96b403b4f28e
    deleteEnvironment:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/deleteEnvironment'
          example:
            environment:
              id: 5daabc50-8451-43f6-922d-96b403b4f28e
              uid: 1234567-5daabc50-8451-43f6-922d-96b403b4f28e
    getEnvironmentForks:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/getEnvironmentForks'
          example:
            data:
              - forkId: ********-2fgh78uj-2ee3-8ugc-b678-7742d82e2e1f
                forkName: my fork
                createdAt: '2023-11-16T09:18:17.000Z'
                createdBy: Taylor Lee
                updatedAt: '2023-11-16T09:18:17.000Z'
              - forkId: ********-667df1ad-2ee3-6yhn-b678-7742d82e2e1f
                forkName: fork2
                createdAt: '2023-11-16T09:16:15.000Z'
                createdBy: Taylor Lee
                updatedAt: '2023-11-16T09:16:15.000Z'
            meta:
              total: 6
              nextCursor: null
    environmentForks400Error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/environmentForks400Error'
          examples:
            environmentNotForked:
              value:
                type: https://api.postman.com/problems/bad-request
                title: The environment Id is not a forked environment.
                detail: {}
    environmentForks404Error:
      description: Not Found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/environmentForks404Error'
          example:
            title: We could not find the environment you are looking for
            status: 404
            detail: We could not find the environment you are looking for
            type: https://api.postman.com/problems/notFound
    forkEnvironment:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/forkEnvironmentResponse'
          example:
            environment:
              uid: ********-0fcea3ba-abcf-49c3-9f48-669c208fef25
              name: Testing Environment
              forkName: Test Fork
    mergeEnvironmentFork:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/mergeEnvironmentForkResponse'
          example:
            environment:
              uid: ********-d9c7dc8f-904e-4bba-99b5-4d490aae1957
    pullEnvironment:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/pullEnvironmentResponse'
          example:
            environment:
              uid: ********-d9c7dc8f-904e-4bba-99b5-4d490aae1957
    getGroups:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/getGroups'
          examples:
            Successful Response:
              $ref: '#/components/examples/getGroups'
    common401ErrorUnauthorizedError:
      description: Unauthorized
      content:
        application/problem+json:
          schema:
            type: object
            properties:
              link:
                type: string
                format: uri-reference
                description: The [URI reference](https://www.rfc-editor.org/rfc/rfc3986) that identifies the type of problem.
                example: https://api.postman.com/problems/unauthorized
              title:
                type: string
                description: A short summary of the problem.
                example: unauthorizedError
              detail:
                type: string
                description: Information about the error.
                example: You are not authorized to perform this action.
              status:
                type: integer
                format: http-status-code
                description: The error's HTTP status code.
                example: 401
          example:
            link: https://api.postman.com/problems/unauthorized
            title: unauthorizedError
            detail: You are not authorized to perform this action.
            status: 401
    common403ErrorForbiddenNotAuthorized:
      description: Forbidden
      content:
        application/problem+json:
          schema:
            type: object
            properties:
              link:
                type: string
                format: uri-reference
                description: The [URI reference](https://www.rfc-editor.org/rfc/rfc3986) that identifies the type of problem.
                example: https://api.postman.com/problems/forbidden
              title:
                type: string
                description: A short summary of the problem.
                example: Forbidden
              status:
                type: number
                format: http-status-code
                description: The error's HTTP status code.
                example: 403
              detail:
                type: string
                description: Information about the error.
                example: You are not authorized to perform this action.
          example:
            link: https://api.postman.com/problems/forbidden
            title: Forbidden
            detail: You are not authorized to perform this action.
            status: 403
    common500ErrorServerErrorDetail:
      description: Internal Server Error
      content:
        application/problem+json:
          schema:
            type: object
            properties:
              link:
                type: string
                format: uri-reference
                description: The [URI reference](https://www.rfc-editor.org/rfc/rfc3986) that identifies the type of problem.
                example: https://api.postman.com/problems/internal-server-error
              title:
                type: string
                description: A short summary of the problem.
                example: serverError
              detail:
                type: string
                description: Information about the error.
                example: Something went wrong with the server.
              status:
                type: integer
                format: http-status-code
                description: The error's HTTP status code.
                example: 500
          example:
            link: https://api.postman.com/problems/internal-server-error
            title: serverError
            detail: Something went wrong with the server.
            status: 500
    importOpenApiDefinition:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/importOpenApiDefinition'
          example:
            collections:
              - id: 12ece9e1-2abf-4edc-8e34-de66e74114d2
                name: Test Collection
                uid: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
            environments: []
    getAuthenticatedUser:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/getAuthenticatedUser'
          example:
            user:
              id: ********
              username: taylor-lee
              email: <EMAIL>
              fullName: Taylor Lee
              avatar: https://res.cloudinary.com/postman/image/upload/t_user_profile_300/v1/user/default-6
              isPublic: true
              teamId: 123
              teamName: Test Team
              teamDomain: test-team
              roles:
                - user
                - flow-editor
                - community-manager
            operations:
              - name: api_object_usage
                limit: 99999999
                usage: 8872
                overage: 0
              - name: collection_run_limit
                limit: 99999999
                usage: 226
                overage: 0
              - name: file_storage_limit
                limit: 1
                usage: 0.28
                overage: 0
              - name: flow_requests
                limit: 1000000
                usage: 1852
                overage: 0
              - name: performance_test_limit
                limit: 99999999
                usage: 10
                overage: 0
              - name: reusable_packages
                limit: 100
                usage: 48
                overage: 0
              - name: test_data_retrieval
                limit: 1000
                usage: 180
                overage: 0
              - name: test_data_storage
                limit: 10
                usage: 0.55
                overage: 0
              - name: postbot_calls
                limit: 2500
                usage: 0
                overage: 0
              - name: mock_usage
                limit: 1000000
                usage: 79948
                overage: 0
              - name: monitor_request_runs
                limit: 15000000
                usage: 4493710
                overage: 0
              - name: api_usage
                limit: 1000000
                usage: 10788
                overage: 0
    getMocks:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/getMocks'
          example:
            mocks:
              - id: e3d951bf-873f-49ac-a658-b2dcb91d3289
                owner: '********'
                uid: ********-e3d951bf-873f-49ac-a658-b2dcb91d3289
                collection: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
                mockUrl: https://e3d951bf-873f-49ac-a658-b2dcb91d3289.mock.pstmn.io
                name: Test Mock A
                config:
                  headers: []
                  matchBody: false
                  matchQueryParams: true
                  matchWildcards: true
                  delay:
                    type: fixed
                    duration: 140000
                  serverResponseId: ''
                createdAt: '2022-07-25T20:54:30.000Z'
                updatedAt: '2022-07-25T20:54:30.000Z'
                isPublic: false
                environment: ********-5daabc50-8451-43f6-922d-96b403b4f28e
              - id: cf0f8f11-fe0e-455f-99f3-4177f10f0704
                owner: '********'
                uid: ********-cf0f8f11-fe0e-455f-99f3-4177f10f0704
                collection: ********-2cd8f6b1-e85b-46a6-8111-bd62e73e8aa8
                mockUrl: https://cf0f8f11-fe0e-455f-99f3-4177f10f0704.mock.pstmn.io
                name: Test Mock B
                config:
                  headers: []
                  matchBody: false
                  matchHeader: false
                  matchQueryParams: true
                  matchWildcards: true
                  delay: null
                  serverResponseId: 94d0f5d9-dbdf-4046-9a7d-a11f9b78ac65
                createdAt: '2023-01-26T18:59:05.000Z'
                updatedAt: '2023-01-26T18:59:05.000Z'
                isPublic: false
    mockCreateUpdate:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/mockCreateUpdateResponse'
          example:
            mock:
              id: e3d951bf-873f-49ac-a658-b2dcb91d3289
              owner: '********'
              uid: ********-e3d951bf-873f-49ac-a658-b2dcb91d3289
              collection: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
              mockUrl: https://e3d951bf-873f-49ac-a658-b2dcb91d3289.mock.pstmn.io
              name: Test Mock
              config:
                headers:
                  - POST
                matchBody: false
                matchQueryParams: true
                matchWildcards: true
                delay:
                  type: fixed
                  preset: '1'
                  duration: 300
                serverResponseId: 9a291bbe-dc0a-44ba-a3c8-6dbd06a61460
              createdAt: '2022-06-09T19:00:39.000Z'
              updatedAt: '2022-06-09T19:00:39.000Z'
              environment: ********-5daabc50-8451-43f6-922d-96b403b4f28e
    paramMissing400Error:
      description: Bad Request
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                type: object
                properties:
                  name:
                    type: string
                    description: The error name.
                    example: paramMissingError
                  message:
                    type: string
                    description: The error message.
                    example: Parameter is missing in the request.
                  details:
                    type: object
                    description: Information about the error.
                    properties:
                      param:
                        type: array
                        description: Information about the missing parameter.
                        items:
                          type: string
          examples:
            Missing name Parameter:
              value:
                error:
                  name: paramMissingError
                  message: Parameter is missing in the request.
                  details:
                    param:
                      - name
            Missing collection Parameter:
              value:
                error:
                  name: paramMissingError
                  message: Parameter is missing in the request.
                  details:
                    param:
                      - collection
            Missing Mock serverResponse parameter:
              value:
                error:
                  name: paramMissingError
                  message: Parameter is missing in the request.
                  details:
                    param:
                      - serverResponse
            Missing Server Response statusCode parameter:
              value:
                error:
                  name: paramMissingError
                  message: Parameter is missing in the request.
                  details:
                    param:
                      - statusCode
    getMock:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/getMock'
          example:
            mock:
              id: e3d951bf-873f-49ac-a658-b2dcb91d3289
              owner: '********'
              uid: ********-e3d951bf-873f-49ac-a658-b2dcb91d3289
              collection: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
              mockUrl: https://e3d951bf-873f-49ac-a658-b2dcb91d3289.mock.pstmn.io
              name: Test Mock
              config:
                headers: []
                matchBody: false
                matchQueryParams: true
                matchWildcards: true
                delay:
                  type: fixed
                  duration: 140000
                serverResponseId: 94d0f5d9-dbdf-4046-9a7d-a11f9b78ac65
              createdAt: '2022-07-25T20:54:30.000Z'
              updatedAt: '2022-07-25T20:54:30.000Z'
              isPublic: false
              deactivated: false
              environment: ********-5daabc50-8451-43f6-922d-96b403b4f28e
    mock400ErrorInstanceNotFound:
      description: Not Found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/mock400ErrorInstanceNotFound'
          example:
            error:
              name: instanceNotFoundError
              message: The specified mock does not exist.
    deleteMock:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/deleteMock'
          example:
            mock:
              id: e782b64e-406b-4a6c-8fe9-9ebe84aeb706
              uid: ********-e782b64e-406b-4a6c-8fe9-9ebe84aeb706
    getMockCallLogs:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/getMockCallLogs'
          example:
            call-logs:
              - id: c4505a1e-7261-497c-91ff-db4bd51351a6-9545
                responseName: Double check your method and the request path and try again.
                servedAt: '2022-01-17T06:19:30.000Z'
                request:
                  method: POST
                  path: /animals
                response:
                  type: error
                  statusCode: 404
              - id: 0f63f54d-665e-436a-95b4-c1302d7685a9-3925
                responseName: Bad request
                servedAt: '2022-01-17T06:19:22.000Z'
                request:
                  method: POST
                  path: /animals
                response:
                  type: success
                  statusCode: 400
              - id: adab0d30-5c38-43bf-af90-4119925138e2-3795
                responseName: Successful addition of animals to the store
                servedAt: '2022-01-17T06:19:16.000Z'
                request:
                  method: POST
                  path: /animals
                response:
                  type: success
                  statusCode: 200
              - id: dae50669-f4c1-460a-b3a4-3a2445f4f39d-2468
                responseName: Get filtered list of Animals
                servedAt: '2022-01-17T06:18:26.000Z'
                request:
                  method: GET
                  path: /animals?type=dog
                response:
                  type: success
                  statusCode: 200
              - id: a5330463-26e1-4812-a962-e44b569a2054-9894
                responseName: Get Animals
                servedAt: '2022-01-17T06:18:06.000Z'
                request:
                  method: GET
                  path: /animals
                response:
                  type: success
                  statusCode: 200
            meta:
              nextCursor: null
    mock400ErrorLogRetentionPeriodExceeded:
      description: Log Retention Period Exceeded
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/mock400ErrorLogRetentionPeriodExceeded'
          example:
            error:
              name: invalidParamsError
              message: 'Invalid value for parameter: since. Cannot view logs beyond the call log retention period based on your plan i.e. 30 days.'
    publishMock:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/publishUnpublishMockResponse'
          example:
            mock:
              id: e3d951bf-873f-49ac-a658-b2dcb91d3289
    mock400ErrorAlreadyPublished:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/mock400ErrorAlreadyPublished'
          example:
            error:
              name: mockAlreadyPublishedError
              message: This mock is already public.
    unpublishMock:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/publishUnpublishMockResponse'
          example:
            mock:
              id: e3d951bf-873f-49ac-a658-b2dcb91d3289
    mock400ErrorAlreadyUnpublished:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/mock400ErrorAlreadyUnpublished'
          example:
            error:
              name: mockAlreadyUnpublishedError
              message: This mock has already been deleted.
    getMockServerResponses:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/getMockServerResponses'
          example:
            - id: 965cdd16-fe22-4d96-a161-3d05490ac421
              name: Internal Server Error
              statusCode: 500
              createdAt: '2022-08-02T14:57:44.000Z'
              updatedAt: '2022-08-02T14:57:44.000Z'
              createdBy: '********'
              updatedBy: '********'
    mockServerResponse:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/mockServerResponse'
          example:
            createdAt: '2022-08-02T15:08:03.000Z'
            updatedAt: '2022-08-02T15:08:03.000Z'
            id: 965cdd16-fe22-4d96-a161-3d05490ac421
            name: Internal Server Error
            statusCode: 500
            headers:
              - key: Content-Type
                value: application/json
            language: json
            body: |-
              {
                  "message": "Something went wrong; try again later."
              }
            createdBy: '********'
            updatedBy: '********'
            mock: 32cd624d-9986-4f20-9048-89252f722269
    serverResponseNotFound400Error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/serverResponseNotFound400Error'
          example:
            error:
              name: serverResponseNotFoundError
              message: We could not find the mock server response you are looking for.
    deleteMockServerResponse:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/deleteMockServerResponse'
          example:
            id: 965cdd16-fe22-4d96-a161-3d05490ac421
            name: Internal Server Error
            statusCode: 500
            headers: []
            language: json
            body: |-
              {
                  "message": "Something went wrong; try again later."
              }
            createdBy: '20891195'
            updatedBy: '20891195'
            createdAt: '2022-09-21T20:20:09.000Z'
    getMonitors:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/getMonitors'
          example:
            monitors:
              - id: 1e6b6cc1-c760-48e0-968f-4bfaeeae9af1
                name: Test Monitor
                uid: ********-1e6b6cc1-c760-48e0-968f-4bfaeeae9af1
                owner: '********'
              - id: 1e6b6cb7-f13d-4000-acb7-0695757174a8
                name: Postman Echo Monitor
                uid: 87654321-1e6b6cb7-f13d-4000-acb7-0695757174a8
                owner: '87654321'
    createMonitor:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/createMonitorResponse'
          example:
            monitor:
              id: 1e6b6cc1-c760-48e0-968f-4bfaeeae9af1
              name: Test Monitor
              uid: ********-1e6b6cc1-c760-48e0-968f-4bfaeeae9af1
    monitors400CreateErrors:
      description: Bad Request
      content:
        application/json:
          schema:
            anyOf:
              - $ref: '#/components/schemas/monitors400ErrorInvalidCronPattern'
              - $ref: '#/components/schemas/monitors400ErrorInvalidTimezone'
              - $ref: '#/components/schemas/monitors400ErrorInvalidUid'
              - $ref: '#/components/schemas/monitors400ParamMissing'
          examples:
            Invalid Cron Pattern:
              $ref: '#/components/examples/monitors400ErrorInvalidCronPattern'
            Invalid Timezone:
              $ref: '#/components/examples/monitors400ErrorInvalidTimezone'
            Invalid Collection UID:
              $ref: '#/components/examples/monitors400ErrorInvalidCollectionUid'
            Invalid Environment UID:
              $ref: '#/components/examples/monitors400ErrorInvalidEnvironmentUid'
            Missing Parameter:
              $ref: '#/components/examples/monitors400ErrorMissingParameter'
    common403ErrorAndFeatureUnavailable:
      description: Forbidden
      content:
        application/json:
          schema:
            anyOf:
              - $ref: '#/components/schemas/common403Error'
              - $ref: '#/components/schemas/featureUnavailable403Error'
          examples:
            Forbidden:
              value:
                type: https://api.postman.com/problems/forbidden
                title: Forbidden
                detail: Forbidden
                status: 403
            Feature Unavailable:
              value:
                type: https://api.postman.com/problems/forbidden
                title: Forbidden
                detail: This feature isn't available in your region.
                status: 403
    getMonitor:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/getMonitor'
          example:
            monitor:
              id: 1e6b6cc1-c760-48e0-968f-4bfaeeae9af1
              name: Test Monitor
              uid: ********-1e6b6cc1-c760-48e0-968f-4bfaeeae9af1
              owner: ********
              collectionUid: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
              environmentUid: ********-5daabc50-8451-43f6-922d-96b403b4f28e
              options:
                strictSSL: true
                followRedirects: true
                requestTimeout: 3000
                requestDelay: 0
              notifications:
                onError:
                  - email: <EMAIL>
                onFailure:
                  - email: <EMAIL>
              distribution: []
              schedule:
                cron: 0 0 0 * * *
                timezone: America/Chicago
                nextRun: '2022-06-18T05:00:00.000Z'
              lastRun:
                status: failed
                startedAt: '2022-06-17T18:39:52.852Z'
                finishedAt: '2022-06-17T18:39:53.707Z'
                stats:
                  assertions:
                    total: 8
                    failed: 1
                  requests:
                    total: 4
    instanceNotFoundMonitor:
      description: Instance Not Found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/instanceNotFoundMonitor'
          example:
            error:
              name: instanceNotFoundError
              message: The specified monitor does not exist.
    updateMonitor:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/updateMonitorResponse'
          example:
            monitor:
              id: 1e6b6cc1-c760-48e0-968f-4bfaeeae9af1
              name: Test Monitor
              uid: ********-1e6b6cc1-c760-48e0-968f-4bfaeeae9af1
    monitors400ErrorInvalidCronPattern:
      description: Invalid Cron Pattern
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/monitors400ErrorInvalidCronPattern'
          example:
            value:
              error:
                name: cronPatternNotAllowedError
                message: Invalid cron pattern. Enter a valid cron pattern, such as "0 17 * * *"
                details:
                  pattern: '* * * * *'
    deleteMonitor:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/deleteMonitor'
          example:
            monitor:
              id: 1e6b6cc1-c760-48e0-968f-4bfaeeae9af1
              uid: ********-1e6b6cc1-c760-48e0-968f-4bfaeeae9af1
    runMonitor:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/runMonitor'
          examples:
            Successful Response:
              value:
                run:
                  info:
                    jobId: 1ecee76a-e14e-47c0-bddc-256bf690c407
                    monitorId: 1e6b6cc1-c760-48e0-968f-4bfaeeae9af1
                    name: Test Monitor
                    collectionUid: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
                    environmentUid: ********-5daabc50-8451-43f6-922d-96b403b4f28e
                    status: success
                    startedAt: '2022-06-17T19:50:04.019Z'
                    finishedAt: '2022-06-17T19:50:06.439Z'
                  stats:
                    assertions:
                      total: 0
                      failed: 0
                    requests:
                      total: 1
                      failed: 0
                  executions:
                    - id: 1
                      item:
                        name: Sample POST Request
                      request:
                        method: POST
                        url: http://echo.getpostman.com/post
                        headers:
                          Content-Type: application/json
                          User-Agent: PostmanRuntime/7.29.0
                          Accept: '*/*'
                          Cache-Control: no-cache
                          Postman-Token: null
                          Host: echo.getpostman.com
                          Accept-Encoding: gzip, deflate, br
                          Connection: keep-alive
                          Content-Length: '0'
                        body:
                          contentLength: 0
                        timestamp: '2022-06-17T19:50:06.186Z'
                      response:
                        code: 200
                        body:
                          contentLength: 50
                        responseTime: 49
                        responseSize: 50
                        headers:
                          Date: Fri, 17 Jun 2022 19:50:06 GMT
                          Content-Type: text/plain
                          Content-Length: '50'
                          Connection: keep-alive
                          Server: null
                  failures: []
            Run With async Parameter:
              value:
                run:
                  info:
                    jobId: 1ecee76a-e14e-47c0-bddc-256bf690c407
                    monitorId: 1e6b6cc1-c760-48e0-968f-4bfaeeae9af1
                    name: Test Monitor
                    collectionUid: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
                    environmentUid: ********-5daabc50-8451-43f6-922d-96b403b4f28e
                    status: success
                    startedAt: '2022-06-17T19:50:04.019Z'
                    finishedAt: '2022-06-17T19:50:06.439Z'
    getPanElementsAndFolders:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/getPanElementsAndFolders'
          example:
            elements:
              - addedAt: '2021-11-29T06:31:24.000Z'
                addedBy: ********
                createdBy: ********
                createdAt: '2020-06-01T08:32:31.000Z'
                updatedBy: ********
                updatedAt: '2021-11-29T06:31:24.000Z'
                type: api
                id: 5360b75f-447e-467c-9299-12fd6c92450d
                parentFolderId: 1
                name: Billing API
                summary: Payments and Account Services API
                description: The payments and account services API.
                href: https://api.getpostman.com/apis/5360b75f-447e-467c-9299-12fd6c92450d
              - addedAt: '2021-11-29T06:31:26.000Z'
                addedBy: ********
                createdBy: ********
                createdAt: '2020-06-01T13:59:34.000Z'
                updatedBy: ********
                updatedAt: '2021-11-29T06:31:26.000Z'
                type: api
                id: 41f6dc6d-d8ab-45c6-8506-74a25acf5d5f
                parentFolderId: 2
                name: Reporting API
                summary: The reporting API.
                description: ''
                href: https://api.getpostman.com/apis/41f6dc6d-d8ab-45c6-8506-74a25acf5d5f
            folders:
              - id: 1
                parentFolderId: 0
                updatedAt: '2021-11-29T06:31:24.000Z'
                updatedBy: ********
                createdBy: ********
                createdAt: '2020-06-01T08:32:31.000Z'
                name: Billing
                description: The Billing API.
                type: folder
              - id: 2
                parentFolderId: 0
                updatedAt: '2022-10-17T11:54:51.000Z'
                updatedBy: ********
                createdBy: ********
                createdAt: '2021-03-05T06:27:24.000Z'
                name: Reporting
                description: ''
                type: folder
            meta:
              limit: 1000
              offset: 0
              totalCount: 2
    postPanElementOrFolder:
      description: Created
      content:
        application/json:
          schema:
            oneOf:
              - $ref: '#/components/schemas/panElementCreated'
              - $ref: '#/components/schemas/panFolderCreated'
          examples:
            Add a Collection:
              value:
                addedAt: '2022-12-07T18:22:15.000Z'
                addedBy: ********
                createdBy: ********
                createdAt: '2022-12-07T18:22:15.000Z'
                updatedBy: ********
                updatedAt: '2022-12-07T18:22:15.000Z'
                type: collection
                id: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
                name: Billing API Collection
                summary: The Billing API collection.
                description: ''
                href: https://api.getpostman.com/collections/********-12ece9e1-2abf-4edc-8e34-de66e74114d2
            Create a Folder:
              value:
                id: 1
                parentFolderId: 0
                updatedAt: '2022-12-07T18:00:39.000Z'
                updatedBy: ********
                createdBy: ********
                createdAt: '2022-12-07T18:00:39.000Z'
                name: Billing
                description: The Billing API.
                type: folder
    instanceNotFoundElementFolder:
      description: Not Found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/instanceNotFoundElementFolder'
          example:
            error:
              name: instanceNotFoundError
              message: The specified API does not exist.
    updatePanElementOrFolder:
      description: Successful Response
      content:
        application/json:
          schema:
            oneOf:
              - $ref: '#/components/schemas/panElementCreated'
              - $ref: '#/components/schemas/panFolderCreated'
          examples:
            Update Collection:
              value:
                addedAt: '2022-12-07T18:22:15.000Z'
                addedBy: ********
                createdBy: ********
                createdAt: '2022-12-07T18:22:15.000Z'
                updatedBy: ********
                updatedAt: '2022-12-07T18:22:15.000Z'
                type: collection
                id: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
                name: Billing API Collection
                summary: null
                description: ''
                href: https://api.getpostman.com/collections/********-12ece9e1-2abf-4edc-8e34-de66e74114d2
                parentFolderId: 1
            Update Folder:
              value:
                id: 1
                parentFolderId: 0
                updatedAt: '2022-12-07T20:27:06.000Z'
                updatedBy: ********
                createdBy: ********
                createdAt: '2022-12-07T17:04:23.000Z'
                name: Billing
                description: The Billing API.
                type: folder
    deletePanElementOrFolder:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/deletePanElementOrFolder'
          examples:
            Remove an API:
              value:
                api:
                  id: 5360b75f-447e-467c-9299-12fd6c92450d
            Delete a Folder:
              value:
                folder:
                  id: '1'
    panFolder400ErrorNotEmpty:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/panFolder400ErrorNotEmpty'
          example:
            name: folderNotEmptyError
            message: You need to empty this folder before deleting it.
    getAllPanAddElementRequests:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/getAllPanAddElementRequests'
          example:
            requests:
              - id: 1
                createdBy: 87654321
                createdAt: '2020-06-01T08:32:31.000Z'
                status: pending
                element:
                  type: api
                  id: 360b75f-447e-467c-9299-12fd3c92450d
                  name: Test api
                  summary: Test api summary
              - id: 2
                createdBy: ********
                createdAt: '2022-06-09T14:48:45.000Z'
                message: Please approve this collection
                status: denied
                element:
                  type: collection
                  id: 5360b75f-447e-467c-9299-12fd3c92450d
                  name: Test Collection
                  summary: This is a test collection.
                response:
                  createdAt: '2020-06-01T08:32:31.000Z'
                  createdBy: 2272
                  message: Too many errors, please fix and resubmit
            meta:
              limit: 10
              offset: 5
              totalCount: 100
    pan400ErrorInvalidQueryParams:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/pan400ErrorInvalidQueryParams'
          example:
            name: invalidParamsError
            message: The specified request does not exist.
    respondPanElementAddRequest:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/respondPanElementAddRequestResponse'
          example:
            request:
              - id: 2
                createdAt: '2022-06-09T14:48:45.000Z'
                createdBy: ********
                message: Please add this to the network
                status: denied
                element:
                  type: api
                  id: 5360b75f-447e-467c-9299-12fd3c92450d
                  name: Test API
                  summary: This is a test API
                response:
                  createdAt: '2022-06-09T14:48:45.000Z'
                  createdBy: 87654321
                  message: Please fix the API issues first
    pan400ErrorInvalidParams:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/pan400ErrorInvalidParams'
          example:
            name: invalidParamsError
            message: Status type can only be denied or approved.
    getPullRequest:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/pullRequestGet'
          example:
            createdAt: '2024-02-20T09:55:46.000Z'
            updatedAt: '2024-02-20T09:58:38.000Z'
            id: 4e1a6609-1a29-4037-a411-89ecc14c6cd8
            title: Test PR
            description: This is a test pull request.
            createdBy: '********'
            updatedBy: '********'
            comment: Missing descriptions in requests
            forkType: collection
            source:
              id: 87654321-3b79068c-dbe5-41d5-a826-51be4bf646ef
              name: test-collection
              forkName: Taylor Lee's fork
              exists: true
            destination:
              id: ********9-24f57217-1169-4b7c-a810-0e957c04eaa5
              name: test-collection
              exists: true
            status: declined
            merge:
              status: inactive
            reviewers:
              - id: '********'
                status: declined
    pullRequestUpdate:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/pullRequestUpdated'
          example:
            createdAt: '2024-02-20T09:58:57.000Z'
            createdBy: '********'
            description: Updated description of the pull request.
            destinationId: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
            forkType: collection
            id: 4e1a6609-1a29-4037-a411-89ecc14c6cd8
            sourceId: 87654321-3b79068c-dbe5-41d5-a826-51be4bf646ef
            status: open
            title: Updated PR title
            updatedAt: '2024-02-20T09:58:57.000Z'
    pullRequest409ErrorConflict:
      description: Conflict
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/pullRequest409ErrorConflict'
          example:
            detail: You cannot modify a pull request which is already merged or declined.
            status: 409
            title: Conflict
            type: https://api.postman.com/problems/conflict
    pullRequestReview200OK:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/pullRequestReviewReponse'
          examples:
            Approved:
              value:
                id: 4e1a6609-1a29-4037-a411-89ecc14c6cd8
                reviewedBy:
                  id: ********
                  name: Taylor Lee
                  username: taylor-lee
                status: approved
                updatedAt: '2024-02-21T08:19:09.000Z'
            Merged:
              value:
                id: 4e1a6609-1a29-4037-a411-89ecc14c6cd8
                reviewedBy:
                  id: ********
                  name: Taylor Lee
                  username: taylor-lee
                status: merged
                updatedAt: '2024-02-21T08:19:09.000Z'
            Declined:
              value:
                id: 4e1a6609-1a29-4037-a411-89ecc14c6cd8
                reviewedBy:
                  id: ********
                  name: Taylor Lee
                  username: taylor-lee
                status: declined
                updatedAt: '2024-02-21T08:19:09.000Z'
            Unpproved:
              value:
                id: 4e1a6609-1a29-4037-a411-89ecc14c6cd8
                reviewedBy:
                  id: ********
                  name: Taylor Lee
                  username: taylor-lee
                status: open
                updatedAt: '2024-02-21T08:19:09.000Z'
    getSecretTypes:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/getSecretTypes'
          example:
            meta:
              total: 4
            data:
              - name: Postman Collection Key
                id: 04084949-0dda-4735-ace5-f83038f7b200
                type: DEFAULT
              - name: Planetscale API Token
                id: 082351ca-810e-476b-8437-d098b20fba4b
                type: DEFAULT
              - name: Open AI API Key
                id: 0987c237-c1d0-4f07-a1af-a5772facf866
                type: DEFAULT
              - name: Picatic API Key
                id: 0eac968d-f65e-4f3d-9a34-c02138072835
                type: DEFAULT
    schemaSecurityValidation:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/schemaSecurityValidationResponse'
          examples:
            No Warnings Found:
              $ref: '#/components/examples/schemaSecurityValidationNoWarnings'
            Successful Response with Governance Warnings:
              $ref: '#/components/examples/schemaSecurityValidationGovernanceWarnings'
            Successful Response with Security Warnings:
              $ref: '#/components/examples/schemaSecurityValidationSecurityWarnings'
    schemaSecurityValidation400Error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/schemaSecurityValidation400Error'
          examples:
            Invalid Schema:
              $ref: '#/components/examples/schemaSecurityValidation400Error'
    getTaggedEntities:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/getTaggedEntities'
          example:
            data:
              entities:
                - entityId: 8b86dfe8-de72-44e6-81ea-79d19805bc6a
                  entityType: api
            meta:
              count: 1
    tagElement400Error:
      description: Bad Request
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/tagElement400Error'
          example:
            error: Bad Request
            message: params/slug must match pattern \"^[a-z][a-z0-9-]*[a-z0-9]+$\"
            statusCode: 400
    getScimGroupResources:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/getScimGroupResources'
          example:
            schemas:
              - urn:ietf:params:scim:api:messages:2.0:ListResponse
            totalResults: 2
            startIndex: 1
            itemsPerPage: 2
            Resources:
              - schemas:
                  - urn:ietf:params:scim:schemas:core:2.0:Group
                id: 561631fq14ed41872a8eea4c8aa2b38cda9749812cc55c00
                displayName: Test-API
                members:
                  - value: 23a35c2723d34c03b4c56443c09e7173
                    display: Taylor Lee
                externalId: '1234'
                meta:
                  resourceType: Group
                  created: '2022-02-22T04:24:13.000Z'
                  lastModified: '2022-02-22T04:24:13.000Z'
              - schemas:
                  - urn:ietf:params:scim:schemas:core:2.0:Group
                id: 123775fe15ed41872a8eea4c8aa2b38cda9749812cc55c99
                displayName: Test Group
                members:
                  - value: 23a35c2723d34c03b4c56443c09e7173
                    display: Taylor Lee
                  - value: b1c794f24f4c49f4b5d503a4cb2686ea
                    display: Alex Cruz
                externalId: '4321'
                meta:
                  resourceType: Group
                  created: '2022-02-22T04:24:13.000Z'
                  lastModified: '2022-02-22T04:24:13.000Z'
    scim400ErrorInvalidFilter:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/scim400ErrorInvalidFilter'
          example:
            schemas:
              - urn:ietf:params:scim:api:messages:2.0:Error
            detail: You've used filter(s) that Postman doesn't support.
            status: '400'
    scim401Error:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/scim401Error'
          example:
            schemas:
              - urn:ietf:params:scim:api:messages:2.0:Error
            detail: Unable to access the team. Check if you have entered a valid API key.
            status: '401'
    scim403Error:
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/scim403Error'
          example:
            schemas:
              - urn:ietf:params:scim:api:messages:2.0:Error
            detail: Your API key was generated by a Team Admin who is no longer on your team. Use an API key generated by a Team Admin.
            status: '403'
    scimGroup500Error:
      description: Internal Server Error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/scimGroup500Error'
          example:
            schemas:
              - urn:ietf:params:scim:api:messages:2.0:Error
            detail: Couldn't fetch members of the team.
            status: '500'
    createScimGroup:
      description: Created
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/createScimGroupResponse'
          example:
            schemas:
              - urn:ietf:params:scim:schemas:core:2.0:Group
            id: 561631fq14ed41872a8eea4c8aa2b38cda9749812cc55c00
            displayName: Test-API
            externalId: '1234'
            members:
              - value: 23a35c2723d34c03b4c56443c09e7173
                display: Taylor Lee
            meta:
              created: '2022-02-22T04:24:13.000Z'
              lastModified: '2022-02-22T04:24:13.000Z'
              resourceType: Group
    scim400ErrorInvalidSyntax:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/scim400ErrorInvalidSyntax'
          example:
            schemas:
              - urn:ietf:params:scim:api:messages:2.0:Error
            scimType: invalidSyntax
            detail: The request body seems to be incomplete or have unsupported characters.
            status: '400'
    scim409Error:
      description: Conflict
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/scim409Error'
          example:
            schemas:
              - urn:ietf:params:scim:api:messages:2.0:Error
            scimType: uniqueness
            detail: This person is already a member of the team.
            status: '409'
    scimUpdateGroup500Error:
      description: Internal Server Error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/scimUpdateGroup500Error'
          example:
            schemas:
              - urn:ietf:params:scim:api:messages:2.0:Error
            detail: Couldn't update this team member's information. Try again — it should work next time around.
            status: '500'
    getScimGroupResource:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/getScimGroupResource'
          example:
            schemas:
              - urn:ietf:params:scim:schemas:core:2.0:Group
            id: 405775fe15ed41872a8eea4c8aa2b38cda9749812cc55c99
            displayName: Test-API
            members:
              - value: 23a35c2723d34c03b4c56443c09e7173
                display: Taylor Lee
            externalId: '1234'
            meta:
              resourceType: Group
              created: '2022-02-22T04:24:13.000Z'
              lastModified: '2022-02-22T04:24:13.000Z'
    scim400Error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/scim400Error'
          example:
            schemas:
              - urn:ietf:params:scim:api:messages:2.0:Error
            detail: This Postman team doesn't exist anymore.
            status: '400'
    scimGroup404Error:
      description: Not Found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/scimGroup404Error'
          example:
            schemas:
              - urn:ietf:params:scim:api:messages:2.0:Error
            detail: This group isn't a member of the team.
            status: '404'
    scim500Error:
      description: Internal Server Error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/scim500Error'
          example:
            schemas:
              - urn:ietf:params:scim:api:messages:2.0:Error
            detail: Couldn't update this team member's information. Try again — it should work next time around.
            status: '500'
    scimUpdateGroup:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/scimUpdateGroupResponse'
          example:
            schemas:
              - urn:ietf:params:scim:schemas:core:2.0:Group
            id: 405775fe15ed41872a8eea4c8aa2b38cda9749812cc55c99
            displayName: Test-API
            members:
              - value: 23a35c2723d34c03b4c56443c09e7173
                display: Taylor Lee
            externalId: '1234'
            meta:
              resourceType: Group
              created: '2022-02-22T04:24:13.000Z'
              lastModified: '2022-02-22T04:24:13.000Z'
    getScimResourceTypes:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/getScimResourceTypes'
          example:
            - schemas:
                - urn:ietf:params:scim:schemas:core:2.0:ResourceType
              id: User
              name: User
              endpoint: /Users
              description: User Account
              schema: urn:ietf:params:scim:schemas:core:2.0:User
              schemaExtensions:
                - schema: urn:ietf:params:scim:schemas:extension:enterprise:2.0:User
                  required: true
            - schemas:
                - urn:ietf:params:scim:schemas:core:2.0:ResourceType
              id: Group
              name: Group
              endpoint: /Groups
              description: Group
              schema: urn:ietf:params:scim:schemas:core:2.0:Group
    getScimServiceProviderConfig:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/getScimServiceProviderConfig'
          example:
            schemas:
              - urn:ietf:params:scim:schemas:core:2.0:ServiceProviderConfig
            documentationUri: https://learning.postman.com/docs/administration/managing-your-team/configuring-scim
            patch:
              supported: true
            bulk:
              supported: false
              maxOperations: 0
              maxPayloadSize: 0
            filter:
              supported: true
              maxResults: 100
            changePassword:
              supported: false
            sort:
              supported: false
            etag:
              supported: false
            authenticationSchemes:
              - name: OAuth Bearer Token
                description: Authentication scheme using the OAuth Bearer Token Standard
                specUri: http://www.rfc-editor.org/info/rfc6750
                type: oauthbearertoken
            meta:
              resourceType: ServiceProviderConfig
              location: https://api.getpostman.com/scim/v2/ServiceProviderConfig
    createScimUser:
      description: Created
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/createScimUserResponse'
          example:
            schemas:
              - urn:ietf:params:scim:schemas:core:2.0:User
            id: 405775fe15ed41872a8eea4c8aa2b38cda9749812cc55c99
            userName: <EMAIL>
            name:
              givenName: Test
              familyName: User
            externalId: '********'
            active: true
            meta:
              resourceType: User
              created: '2021-02-22T04:24:13.000Z'
              lastModified: '2021-02-22T04:24:13.000Z'
    scimUserResource:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/scimUserResourceResponse'
          example:
            schemas:
              - urn:ietf:params:scim:schemas:core:2.0:User
            id: 405775fe15ed41872a8eea4c8aa2b38cda9749812cc55c99
            userName: <EMAIL>
            name:
              givenName: Taylor
              familyName: Lee
            externalId: '********'
            active: true
            meta:
              resourceType: User
              created: '2021-02-22T04:24:13.000Z'
              lastModified: '2021-02-22T04:24:13.000Z'
    scim404Error:
      description: Not Found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/scim404Error'
          example:
            schemas:
              - urn:ietf:params:scim:api:messages:2.0:Error
            detail: This person isn't a member of the team.
            status: '404'
    scim500ErrorUser:
      description: Internal Server Error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/scim500ErrorUser'
          example:
            schemas:
              - urn:ietf:params:scim:api:messages:2.0:Error
            detail: Unable to fetch information about this team member.
            status: '500'
    scim501ErrorUser:
      description: Not Implemented
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/scim501ErrorUser'
          example:
            schemas:
              - urn:ietf:params:scim:api:messages:2.0:Error
            scimType: invalidSyntax
            detail: Service Provider does not support the request operation DELETE.
            status: '501'
    getTeamUsers:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/getTeamUsers'
          examples:
            Successful Response:
              $ref: '#/components/examples/getTeamUsers'
    createWebhook:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/createWebhookResponse'
          example:
            webhook:
              id: 1f0df51a-8658-4ee8-a2a1-d2567dfa09a9
              name: Test Webhook
              collection: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
              webhookUrl: https://newman-api.getpostman.com/run/********/267a6e99-b6da-407c-a96f-03be2d6282fb
              uid: ********-1f0df51a-8658-4ee8-a2a1-d2567dfa09a9
    createWebhookParamMissing400Error:
      description: Missing Parameter
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/createWebhookParamMissing400Error'
          example:
            error:
              name: paramMissingError
              message: Parameter is missing in the request.
              details:
                param:
                  - collection
    getWorkspaces:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/getWorkspaces'
          examples:
            Successful Response:
              value:
                workspaces:
                  - id: 1f0df51a-8658-4ee8-a2a1-d2567dfa09a9
                    name: My Workspace
                    type: personal
                    visibility: personal
                    createdBy: ********
                  - id: a0f46158-1529-11ee-be56-0242ac120002
                    name: Private Workspace
                    type: team
                    visibility: private
                    createdBy: ********
                  - id: f8801e9e-03a4-4c7b-b31e-5db5cd771696
                    name: Team Workspace
                    type: team
                    visibility: team
                    createdBy: ********
                  - id: 74dbfab8-1529-11ee-be56-0242ac120002
                    name: Public Workspace
                    type: team
                    visibility: public
                    createdBy: ********
                  - id: 74dbfab8-1529-11ee-be56-0242ac120002
                    name: Partner Workspace
                    type: team
                    visibility: partner
                    createdBy: ********
            Include SCIM IDs:
              value:
                workspaces:
                  - id: 1f0df51a-8658-4ee8-a2a1-d2567dfa09a9
                    name: My Workspace
                    type: personal
                    visibility: personal
                    createdBy: ********
                    scim:
                      createdBy: 405775fe15ed41872a8eea4c8aa2b38cda9749812cc55c99
    createWorkspace:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/createWorkspaceResponse'
          example:
            workspace:
              id: 1f0df51a-8658-4ee8-a2a1-d2567dfa09a9
              name: Team Workspace
    workspace400ErrorMalformedRequest:
      description: Workspace Not Found
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/workspace400ErrorMalformedRequest'
          example:
            name: malformedRequestError
            message: '''workspace'' object missing in the request'
    workspace403ErrorUnauthorized:
      description: Forbidden
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/workspace403ErrorUnauthorized'
          example:
            error:
              status: 403
              name: forbiddenError
              message: You are not authorized to perform this action
              detail: You do not have permission to create team workspaces.
              instance: ''
    getAllWorkspaceRoles:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/getAllWorkspaceRoles'
          example:
            roles:
              user:
                - id: '3'
                  displayName: Admin
                  description: Can manage workspace details and members.
                - id: '1'
                  displayName: Viewer
                  description: Can view, fork, and export workspace resources.
                - id: '2'
                  displayName: Editor
                  description: Can create and edit workspace resources.
              usergroup:
                - id: '3'
                  displayName: Admin
                  description: Can manage workspace details and members.
                - id: '1'
                  displayName: Viewer
                  description: Can view, fork, and export workspace resources.
                - id: '2'
                  displayName: Editor
                  description: Can create and edit workspace resources.
    getWorkspace:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/getWorkspace'
          examples:
            Successful Response:
              value:
                workspace:
                  id: 1f0df51a-8658-4ee8-a2a1-d2567dfa09a9
                  name: Team Workspace
                  type: team
                  description: This is a team workspace.
                  visibility: team
                  createdBy: '********'
                  updatedBy: '********'
                  createdAt: '2022-07-06T16:18:32.000Z'
                  updatedAt: '2022-07-06T20:55:13.000Z'
                  collections:
                    - id: 12ece9e1-2abf-4edc-8e34-de66e74114d2
                      name: Test Collection
                      uid: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
                  environments:
                    - id: 5daabc50-8451-43f6-922d-96b403b4f28e
                      name: Test Environment
                      uid: ********-5daabc50-8451-43f6-922d-96b403b4f28e
                  mocks:
                    - id: e3d951bf-873f-49ac-a658-b2dcb91d3289
                      name: Test Mock
                      uid: ********-e3d951bf-873f-49ac-a658-b2dcb91d3289
                      deactivated: false
                  monitors:
                    - id: 1e6b6cc1-c760-48e0-968f-4bfaeeae9af1
                      name: Test Monitor
                      uid: ********-1e6b6cc1-c760-48e0-968f-4bfaeeae9af1
                      deactivated: false
                  apis:
                    - id: 387c2863-6ee3-4a56-8210-225f774edade
                      name: Test API
                      uid: ********-387c2863-6ee3-4a56-8210-225f774edade
            Include SCIM IDs:
              value:
                workspace:
                  id: 1f0df51a-8658-4ee8-a2a1-d2567dfa09a9
                  name: Team Workspace
                  type: team
                  description: This is a team workspace.
                  visibility: team
                  createdBy: '********'
                  updatedBy: '********'
                  createdAt: '2022-07-06T16:18:32.000Z'
                  updatedAt: '2022-07-06T20:55:13.000Z'
                  collections:
                    - id: 12ece9e1-2abf-4edc-8e34-de66e74114d2
                      name: Test Collection
                      uid: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
                  environments:
                    - id: 5daabc50-8451-43f6-922d-96b403b4f28e
                      name: Test Environment
                      uid: ********-5daabc50-8451-43f6-922d-96b403b4f28e
                  mocks:
                    - id: e3d951bf-873f-49ac-a658-b2dcb91d3289
                      name: Test Mock
                      uid: ********-e3d951bf-873f-49ac-a658-b2dcb91d3289
                      deactivated: false
                  monitors:
                    - id: 1e6b6cc1-c760-48e0-968f-4bfaeeae9af1
                      name: Test Monitor
                      uid: ********-1e6b6cc1-c760-48e0-968f-4bfaeeae9af1
                      deactivated: false
                  apis:
                    - id: 387c2863-6ee3-4a56-8210-225f774edade
                      name: Test API
                      uid: ********-387c2863-6ee3-4a56-8210-225f774edade
                  scim:
                    createdBy: 405775fe15ed41872a8eea4c8aa2b38cda9749812cc55c99
                    updatedBy: 405775fe15ed41872a8eea4c8aa2b38cda9749812cc55c99
    workspace404ErrorNotFound:
      description: Not Found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/workspace404ErrorNotFound'
          example:
            error:
              name: workspaceNotFoundError
              message: Workspace not found
              statusCode: 404
    updateWorkspace:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/updateWorkspaceResponse'
          example:
            workspace:
              id: 1f0df51a-8658-4ee8-a2a1-d2567dfa09a9
              name: Test Workspace
    workspace403Error:
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/workspace403Error'
          examples:
            forbiddenError:
              value:
                error:
                  name: forbiddenError
                  message: You do not have access to update this workspace.
            cannotConvertToPublicWorkspace:
              value:
                error:
                  name: cannotConvertFromPublicWorkspace
                  message: Can not convert workspaces to public workspaces for team user. First convert to team.
            cannotConvertFromPublicWorkspace:
              value:
                error:
                  name: cannotConvertFromPublicWorkspace
                  message: Can not convert workspaces from public workspaces for team user. First convert to team.
    instanceNotFoundWorkspace:
      description: Instance Not Found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/instanceNotFoundWorkspace'
          example:
            error:
              name: instanceNotFoundError
              message: The specified workspace does not exist.
    deleteWorkspace:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/deleteWorkspace'
          example:
            workspace:
              id: 1f0df51a-8658-4ee8-a2a1-d2567dfa09a9
    workspace400Error:
      description: Not Found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/workspace400Error'
          example:
            error:
              name: instanceNotFoundError
              message: The specified workspace does not exist.
    getWorkspaceGlobalVariables:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/getWorkspaceGlobalVariables'
          example:
            values:
              - key: api-key
                type: secret
                value: PMAK-XXXX
                enabled: true
              - key: collection_uid
                type: default
                value: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
                enabled: true
    globalVariables500Error:
      description: Internal Server Error
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/globalVariables500Error'
          example:
            type: https://api.postman.com/problems/servererror
            title: Internal server error
            detail: Details about the error
    updateWorkspaceGlobalVariables:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/updateWorkspaceGlobalVariablesResponse'
          example:
            values:
              - key: api-key
                type: secret
                value: PMAK-XXXX
                enabled: true
              - key: collection_uid
                type: default
                value: ********-12ece9e1-2abf-4edc-8e34-de66e74114d2
                enabled: true
    getWorkspaceRoles:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/getWorkspaceRoles'
          examples:
            Successful Response:
              $ref: '#/components/examples/workspaceRoles'
            Return SCIM IDs:
              $ref: '#/components/examples/workspaceRolesScimIds'
    resourceNotFound404Error:
      description: Not Found
      content:
        application/json:
          schema:
            type: object
            properties:
              type:
                type: string
                format: uri-reference
                description: The [URI reference](https://www.rfc-editor.org/rfc/rfc3986) that identifies the type of problem.
                example: https://api.postman.com/problems/not-found
              title:
                type: string
                description: A short summary of the problem.
                example: Resource not found
              detail:
                type: string
                description: Information about the error.
                example: ''
              status:
                type: number
                format: http-status-code
                description: The error's HTTP status code.
                example: 404
          example:
            type: https://api.postman.com/problems/not-found
            title: Resource not found
            detail: ''
            status: 404
    updateWorkspaceRoles:
      description: Successful Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/updateWorkspaceRolesResponse'
          examples:
            Update User Role:
              $ref: '#/components/examples/userRoleUpdated'
            Update Role with SCIM ID:
              $ref: '#/components/examples/userRoleUpdatedSCIMId'
            Update Role and Group with SCIM IDs:
              $ref: '#/components/examples/userRoleGroupUpdatedSCIMId'
            Add or Remove Workspace Group Role:
              $ref: '#/components/examples/workspaceRoles'
    workspaceRoles400Error:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/workspaceRoles400Error'
          example:
            type: invalidParamError
            title: body.roles[0] should have required property 'op'
            detail: ''
            status: 400
    workspaceRoles422UnsupportRoleError:
      description: Partner and Personal Workspace Roles Unsupported
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/workspaceRoles422UnsupportRoleError'
          example:
            detail: Roles are not supported for personal and partner workspaces.
            link: https://api.postman.com/problems/unprocessable-entity
            status: 422
            title: Cannot process the request.
