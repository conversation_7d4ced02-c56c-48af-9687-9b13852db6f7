

## Prompts

For each of the CLI pages:

```
@https://learning.postman.com/docs/postman-cli/postman-cli-reporters/

Analyze this plan "Postman API & CLI MCP Server Design Document" to incorporate Postman CLI into this MCP Server tools. Having a better idea of what we're hoping to acheive, revise the design if necessary or update the documented plan for clarity and completeness.

NOTE: See the attached URL for the most current info about "Postman CLI". We are referring to the official command-line only tool and not the opensource Newman command-line tool and library.

IMPORTANT: Do not over engineer a solution. The goal is to provide a simple and easy to use interface to the Postman CLI tool.

@/docs/dev/postman-cli-design-doc.md

@/src/server.ts
```


```
Proofread this plan "Postman API & CLI MCP Server Design Document". Having a better idea of what we're hoping to acheive, revise the design if necessary or update the documented plan for clarity and completeness.

IMPORTANT: Do not over engineer a solution. The goal is to provide a simple and easy to use interface to the Postman CLI tool.

@/docs/dev/postman-cli-design-doc.md

@/src/server.ts
```
