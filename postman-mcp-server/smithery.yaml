# Smithery configuration file: https://smithery.ai/docs/config#smitheryyaml

startCommand:
  type: stdio
  configSchema:
    # JSON Schema defining the configuration options for the MCP.
    type: object
    required:
      - postmanApiKey
    properties:
      postmanApiKey:
        type: string
        description: The API key for authenticating with the Postman API.
  commandFunction:
    # A function that produces the CLI command to start the MCP on stdio.
    |-
    (config) => ({ command: 'node', args: ['build/index.js'], env: { POSTMAN_API_KEY: config.postmanApiKey } })
    
