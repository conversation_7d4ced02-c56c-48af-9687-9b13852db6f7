hoistPattern:
  - '*'
hoistedDependencies:
  asynckit@0.4.0:
    asynckit: private
  bytes@3.1.2:
    bytes: private
  combined-stream@1.0.8:
    combined-stream: private
  content-type@1.0.5:
    content-type: private
  delayed-stream@1.0.0:
    delayed-stream: private
  depd@2.0.0:
    depd: private
  follow-redirects@1.15.9:
    follow-redirects: private
  form-data@4.0.1:
    form-data: private
  http-errors@2.0.0:
    http-errors: private
  iconv-lite@0.6.3:
    iconv-lite: private
  inherits@2.0.4:
    inherits: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  raw-body@3.0.0:
    raw-body: private
  safer-buffer@2.1.2:
    safer-buffer: private
  setprototypeof@1.2.0:
    setprototypeof: private
  statuses@2.0.1:
    statuses: private
  toidentifier@1.0.1:
    toidentifier: private
  undici-types@6.19.8:
    undici-types: private
  unpipe@1.0.0:
    unpipe: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@9.0.6
pendingBuilds: []
prunedAt: Wed, 30 Jul 2025 20:05:52 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped: []
storeDir: /Users/<USER>/Library/pnpm/store/v3
virtualStoreDir: .pnpm
