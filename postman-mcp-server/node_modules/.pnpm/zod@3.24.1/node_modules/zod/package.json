{"name": "zod", "version": "3.24.1", "author": "<PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "git+https://github.com/colinhacks/zod.git"}, "main": "./lib/index.js", "module": "./lib/index.mjs", "devDependencies": {"@babel/core": "^7.22.5", "@babel/preset-env": "^7.22.5", "@babel/preset-typescript": "^7.22.5", "@jest/globals": "^29.4.3", "@rollup/plugin-typescript": "^8.2.0", "@standard-schema/spec": "^1.0.0-beta.4", "@swc/core": "^1.3.66", "@swc/jest": "^0.2.26", "@types/benchmark": "^2.1.0", "@types/jest": "^29.2.2", "@types/node": "14", "@typescript-eslint/eslint-plugin": "^5.15.0", "@typescript-eslint/parser": "^5.15.0", "babel-jest": "^29.5.0", "benchmark": "^2.1.4", "dependency-cruiser": "^9.19.0", "eslint": "^8.11.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-ban": "^1.6.0", "eslint-plugin-import": "^2.25.4", "eslint-plugin-simple-import-sort": "^7.0.0", "eslint-plugin-unused-imports": "^2.0.0", "husky": "^7.0.4", "jest": "^29.3.1", "lint-staged": "^12.3.7", "netlify-cli": "^17.26.2", "nodemon": "^2.0.15", "prettier": "^2.6.0", "pretty-quick": "^3.1.3", "rollup": "^2.70.1", "ts-jest": "^29.1.0", "ts-morph": "^14.0.0", "ts-node": "^10.9.1", "tslib": "^2.3.1", "tsx": "^3.8.0", "typescript": "^5.0.0", "vitest": "^0.32.2"}, "exports": {".": {"types": "./index.d.ts", "require": "./lib/index.js", "import": "./lib/index.mjs"}, "./package.json": "./package.json", "./locales/*": "./lib/locales/*"}, "bugs": {"url": "https://github.com/colinhacks/zod/issues"}, "description": "TypeScript-first schema declaration and validation library with static type inference", "files": ["/lib", "/index.d.ts"], "funding": "https://github.com/sponsors/colinhacks", "homepage": "https://zod.dev", "keywords": ["typescript", "schema", "validation", "type", "inference"], "license": "MIT", "lint-staged": {"src/*.ts": ["eslint --cache --fix", "prettier --ignore-unknown --write"], "*.md": ["prettier --ignore-unknown --write"]}, "scripts": {"prettier:check": "prettier --check src/**/*.ts deno/lib/**/*.ts *.md --no-error-on-unmatched-pattern", "prettier:fix": "prettier --write src/**/*.ts deno/lib/**/*.ts *.md --ignore-unknown --no-error-on-unmatched-pattern", "lint:check": "eslint --cache --ext .ts ./src", "lint:fix": "eslint --cache --fix --ext .ts ./src", "check": "yarn lint:check && yarn prettier:check", "fix": "yarn lint:fix && yarn prettier:fix", "clean": "rm -rf lib/* deno/lib/*", "build": "yarn run clean && npm run build:cjs && npm run build:esm && npm run build:deno", "build:deno": "node ./deno-build.mjs && cp ./README.md ./deno/lib", "build:esm": "rollup --config ./configs/rollup.config.js", "build:cjs": "tsc -p ./configs/tsconfig.cjs.json", "build:types": "tsc -p ./configs/tsconfig.types.json", "build:test": "tsc -p ./configs/tsconfig.test.json", "test:watch": "yarn test:ts-jest --watch", "test": "yarn test:ts-jest", "test:babel": "jest --coverage --config ./configs/babel-jest.config.json", "test:bun": "bun test src/", "test:vitest": "npx vitest --config ./configs/vitest.config.ts", "test:ts-jest": "npx jest --config ./configs/ts-jest.config.json", "test:swc": "npx jest --config ./configs/swc-jest.config.json", "test:deno": "cd deno && deno test", "prepublishOnly": "npm run test && npm run build && npm run build:deno", "play": "nodemon -e ts -w . -x tsx playground.ts", "depcruise": "depcruise -c .dependency-cruiser.js src", "benchmark": "tsx src/benchmarks/index.ts", "prepare": "husky install", "docs": "netlify dev"}, "sideEffects": false, "support": {"backing": {"npm-funding": true}}, "types": "./index.d.ts"}