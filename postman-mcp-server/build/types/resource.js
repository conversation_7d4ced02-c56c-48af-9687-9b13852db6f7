import { McpError, ErrorCode } from '@modelcontextprotocol/sdk/types.js';
/**
 * Base class for implementing resource handlers
 */
export class BaseResourceHandler {
    validateUri(uri) {
        try {
            this.parseUri(uri);
            return true;
        }
        catch {
            return false;
        }
    }
    parseUri(uri) {
        const match = uri.match(/^([a-zA-Z]+):\/\/([^/]+)(?:\/(.+))?$/);
        if (!match) {
            throw new McpError(ErrorCode.InvalidRequest, `Invalid URI format: ${uri}`);
        }
        const [, protocol, resourceType, path] = match;
        const params = {};
        if (path) {
            const segments = path.split('/');
            segments.forEach((segment, index) => {
                if (segment.startsWith('{') && segment.endsWith('}')) {
                    const paramName = segment.slice(1, -1);
                    const nextSegment = segments[index + 1];
                    if (nextSegment && !nextSegment.startsWith('{')) {
                        params[paramName] = nextSegment;
                    }
                }
            });
        }
        return { protocol, resourceType, params };
    }
}
