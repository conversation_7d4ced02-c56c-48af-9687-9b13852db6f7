// Schema type enums
export var ApiSchemaType;
(function (ApiSchemaType) {
    ApiSchemaType["PROTO2"] = "proto:2";
    ApiSchemaType["PROTO3"] = "proto:3";
    ApiSchemaType["GRAPHQL"] = "graphql";
    ApiSchemaType["OPENAPI_3_1"] = "openapi:3_1";
    ApiSchemaType["OPENAPI_3"] = "openapi:3";
    ApiSchemaType["OPENAPI_2"] = "openapi:2";
    ApiSchemaType["OPENAPI_1"] = "openapi:1";
    ApiSchemaType["RAML_1"] = "raml:1";
    ApiSchemaType["RAML_0_8"] = "raml:0_8";
    ApiSchemaType["WSDL_2"] = "wsdl:2";
    ApiSchemaType["WSDL_1"] = "wsdl:1";
    ApiSchemaType["ASYNCAPI_2"] = "asyncapi:2";
})(ApiSchemaType || (ApiSchemaType = {}));
// Collection operation types
export var ApiCollectionOperationType;
(function (ApiCollectionOperationType) {
    ApiCollectionOperationType["COPY"] = "COPY_COLLECTION";
    ApiCollectionOperationType["CREATE"] = "CREATE_NEW";
    ApiCollectionOperationType["GENERATE"] = "GENERATE_FROM_SCHEMA";
})(ApiCollectionOperationType || (ApiCollectionOperationType = {}));
