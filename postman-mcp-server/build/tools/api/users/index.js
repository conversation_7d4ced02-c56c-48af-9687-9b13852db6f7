import { McpError, ErrorCode } from '@modelcontextprotocol/sdk/types.js';
import { BasePostmanTool } from '../base.js';
import { TOOL_DEFINITIONS } from './definitions.js';
export class UserTools extends BasePostmanTool {
    constructor(existingClient) {
        super(null, {}, existingClient);
    }
    getToolDefinitions() {
        return TOOL_DEFINITIONS;
    }
    createResponse(data) {
        return {
            content: [{ type: 'text', text: JSON.stringify(data, null, 2) }]
        };
    }
    async handleToolCall(name, args) {
        try {
            switch (name) {
                case 'get_user_info':
                    return await this.getUserInfo();
                default:
                    throw new McpError(ErrorCode.MethodNotFound, `Unknown tool: ${name}`);
            }
        }
        catch (error) {
            // Let base class interceptor handle API errors
            throw error;
        }
    }
    async getUserInfo() {
        const response = await this.client.get('/me');
        return this.createResponse(response.data);
    }
}
