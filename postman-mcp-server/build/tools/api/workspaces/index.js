import { <PERSON>rror<PERSON><PERSON>, McpError } from '@modelcontextprotocol/sdk/types.js';
import { BasePostmanTool } from '../base.js';
import { TOOL_DEFINITIONS } from './definitions.js';
export class WorkspaceTools extends BasePostmanTool {
    constructor(existingClient) {
        super(null, {}, existingClient);
    }
    getToolDefinitions() {
        return TOOL_DEFINITIONS;
    }
    createResponse(data) {
        return {
            content: [{ type: 'text', text: JSON.stringify(data, null, 2) }]
        };
    }
    async handleToolCall(name, args) {
        try {
            switch (name) {
                case 'list_workspaces':
                    return await this.listWorkspaces(args);
                case 'get_workspace':
                    return await this.getWorkspace(args.workspace, args.include);
                case 'create_workspace':
                    return await this.createWorkspace(args);
                case 'update_workspace':
                    return await this.updateWorkspace(args.workspace_id, args);
                case 'delete_workspace':
                    return await this.deleteWorkspace(args.workspace_id);
                case 'get_global_variables':
                    return await this.getGlobalVariables(args.workspace_id);
                case 'update_global_variables':
                    return await this.updateGlobalVariables(args.workspace_id, args.variables);
                case 'get_workspace_roles':
                    return await this.getWorkspaceRoles(args.workspace_id, args.includeScimQuery);
                case 'update_workspace_roles':
                    return await this.updateWorkspaceRoles(args.workspace_id, args.operations, args.identifierType);
                case 'get_all_workspace_roles':
                    return await this.getAllWorkspaceRoles();
                default:
                    throw new McpError(ErrorCode.MethodNotFound, `Unknown tool: ${name}`);
            }
        }
        catch (error) {
            // Let base class interceptor handle API errors
            throw error;
        }
    }
    async listWorkspaces(params) {
        const response = await this.client.get('/workspaces', { params });
        return this.createResponse(response.data);
    }
    async getWorkspace(workspace_id, include) {
        if (!workspace_id) {
            throw new McpError(ErrorCode.InvalidParams, 'workspace_id is required');
        }
        const response = await this.client.get(`/workspaces/${workspace_id}`, {
            params: { include }
        });
        return this.createResponse(response.data);
    }
    async createWorkspace(data) {
        if (!data.name) {
            throw new McpError(ErrorCode.InvalidParams, 'name is required');
        }
        const response = await this.client.post('/workspaces', data);
        return this.createResponse(response.data);
    }
    async updateWorkspace(workspace_id, data) {
        if (!workspace_id) {
            throw new McpError(ErrorCode.InvalidParams, 'workspace_id is required');
        }
        const response = await this.client.put(`/workspaces/${workspace_id}`, data);
        return this.createResponse(response.data);
    }
    async deleteWorkspace(workspace_id) {
        if (!workspace_id) {
            throw new McpError(ErrorCode.InvalidParams, 'workspace_id is required');
        }
        const response = await this.client.delete(`/workspaces/${workspace_id}`);
        return this.createResponse(response.data);
    }
    async getGlobalVariables(workspace_id) {
        if (!workspace_id) {
            throw new McpError(ErrorCode.InvalidParams, 'workspace_id is required');
        }
        const response = await this.client.get(`/workspaces/${workspace_id}/global-variables`);
        return this.createResponse(response.data);
    }
    async updateGlobalVariables(workspace_id, variables) {
        if (!workspace_id) {
            throw new McpError(ErrorCode.InvalidParams, 'workspace_id is required');
        }
        if (!Array.isArray(variables)) {
            throw new McpError(ErrorCode.InvalidParams, 'variables must be an array');
        }
        const response = await this.client.put(`/workspaces/${workspace_id}/global-variables`, { variables });
        return this.createResponse(response.data);
    }
    async getWorkspaceRoles(workspace_id, includeScimQuery) {
        if (!workspace_id) {
            throw new McpError(ErrorCode.InvalidParams, 'workspace_id is required');
        }
        const response = await this.client.get(`/workspaces/${workspace_id}/roles`, {
            params: { includeScimQuery }
        });
        return this.createResponse(response.data);
    }
    async updateWorkspaceRoles(workspace_id, operations, identifierType) {
        if (!workspace_id) {
            throw new McpError(ErrorCode.InvalidParams, 'workspace_id is required');
        }
        if (!Array.isArray(operations)) {
            throw new McpError(ErrorCode.InvalidParams, 'operations must be an array');
        }
        if (operations.length > 50) {
            throw new McpError(ErrorCode.InvalidParams, 'Maximum 50 role operations allowed per request');
        }
        const response = await this.client.patch(`/workspaces/${workspace_id}/roles`, { operations }, { params: { identifierType } });
        return this.createResponse(response.data);
    }
    async getAllWorkspaceRoles() {
        const response = await this.client.get('/workspaces-roles');
        return this.createResponse(response.data);
    }
}
